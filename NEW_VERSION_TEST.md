# 🎯 Тестирование новой версии ArchivePlugin

## ✨ Что изменилось в новой версии:

- **Размер:** 53 KB (было 34 KB) - добавлен новый код
- **SimpleWorldManager:** читает кастомные spawn точки из config.yml
- **Автоматическое создание config.yml** при первом запуске
- **Подробное логирование** в консоли сервера

## 📦 Установка новой версии:

1. **Скопируйте** `ArchivePlugin.jar` (53 KB) в `D:\serv\plugins\`
2. **Удалите старую папку** `D:\serv\plugins\ArchivePlugin\` (если есть)
3. **Перезапустите** сервер

## 🔍 Что проверить после установки:

### Шаг 1: Проверьте создание config.yml
**Путь:** `D:\serv\plugins\ArchivePlugin\config.yml`

**Должен содержать:**
```yaml
# ArchivePlugin Configuration
worlds:
  directory: "archive-worlds"
  auto-load: true
  copy-to-server: true

custom-spawns:
  # Здесь будут ваши spawn точки
```

### Шаг 2: Проверьте логи сервера
В консоли должно быть:
```
[INFO]: [ArchivePlugin] ArchivePlugin has been enabled!
[INFO]: [ArchivePlugin] Loaded world: world_name
[INFO]: [ArchivePlugin] Using default spawn for world_name
```

### Шаг 3: Протестируйте телепортацию
```bash
/archiveworlds
# Выберите мир и телепортируйтесь
```

**В логах должно появиться:**
```
[INFO]: [ArchivePlugin] Teleported PlayerName to default spawn in world_name
```

## 📝 Настройка spawn точек:

### Способ 1: Через config.yml (рекомендуется)
1. **Зайдите в мир** и найдите нужное место
2. **Нажмите F3** и запишите координаты XYZ
3. **Откройте** `D:\serv\plugins\ArchivePlugin\config.yml`
4. **Добавьте:**
```yaml
custom-spawns:
  your_world_name:
    x: 123.5
    y: 64.0
    z: -89.5
    yaw: 180.0
    pitch: 0.0
```
5. **Перезапустите** сервер

### Способ 2: Проверка работы
После настройки в логах должно быть:
```
[INFO]: [ArchivePlugin] Using custom spawn for your_world_name at 123, 64, -89
[INFO]: [ArchivePlugin] Teleported PlayerName to custom spawn in your_world_name
```

## 🎯 Ожидаемые результаты:

### ✅ Что должно работать:
- Создание config.yml при первом запуске
- Меню миров `/archiveworlds`
- Телепортация в миры
- Чтение кастомных spawn точек из config.yml
- Подробные логи в консоли

### ❌ Что пока не работает:
- Команды `/setarchivespawn` (из-за Adventure API)
- GUI кнопка для установки spawn

## 🔧 Отладка:

### Если config.yml не создается:
1. Проверьте права доступа к папке plugins
2. Убедитесь что плагин загружается (зеленый в `/plugins`)

### Если spawn точки не работают:
1. Проверьте синтаксис в config.yml (отступы важны!)
2. Убедитесь что имя мира точно совпадает
3. Проверьте логи на ошибки

### Если телепортация не работает:
1. Проверьте что папка `archive-worlds` существует
2. Убедитесь что в ней есть папки с мирами
3. Проверьте что в папках миров есть `level.dat`

## 📊 Результаты тестирования:

**Заполните после тестирования:**
- [ ] config.yml создался автоматически
- [ ] Меню миров работает
- [ ] Телепортация работает  
- [ ] Логи показывают правильную информацию
- [ ] Кастомные spawn точки работают

## 🎉 Если всё работает:

Теперь у вас есть рабочий плагин с поддержкой кастомных spawn точек через config.yml!
