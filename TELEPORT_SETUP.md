# Настройка телепортации в ArchivePlugin

## Проблема
Когда игрок телепортируется в мир, он попадает на spawn точку (обычно 0,0), но постройки могут быть в других координатах.

## Решения

### 1. Автоматический поиск построек (рекомендуется)
В `config.yml` установите:
```yaml
worlds:
  teleport-strategy: "search"
```

Плагин будет искать блоки, указывающие на активность игрока (сундуки, печи, факелы, двери и т.д.) и телепортировать рядом с ними.

### 2. Ручная настройка координат
Если вы знаете точные координаты ваших построек:

1. Откройте `plugins/ArchivePlugin/config.yml`
2. Найдите секцию `custom-coordinates`
3. Добавьте координаты для каждого мира:

```yaml
worlds:
  teleport-strategy: "custom"
  custom-coordinates:
    my-world: {x: 100, y: 70, z: -50, yaw: 90, pitch: 0}
    another-world: {x: -200, y: 64, z: 300}
    survival-world: {x: 0, y: 80, z: 0}
```

### 3. Поиск самой высокой точки
```yaml
worlds:
  teleport-strategy: "highest"
```

### 4. Использование spawn точки (по умолчанию)
```yaml
worlds:
  teleport-strategy: "spawn"
```

## Как найти координаты ваших построек

1. Зайдите в мир в одиночной игре или на сервере
2. Найдите ваши постройки
3. Нажмите F3 чтобы увидеть координаты
4. Запишите координаты X, Y, Z
5. Добавьте их в config.yml

## Пример настройки

```yaml
worlds:
  teleport-strategy: "custom"
  custom-coordinates:
    # Мир с замком
    castle-world: {x: 256, y: 75, z: -128, yaw: 180, pitch: 0}
    
    # Современный город  
    city-world: {x: 0, y: 64, z: 0, yaw: 0, pitch: 0}
    
    # Ферма
    farm-world: {x: -100, y: 70, z: 200, yaw: 90, pitch: 0}
```

## Перезагрузка конфигурации

После изменения config.yml:
1. Перезапустите сервер, ИЛИ
2. Используйте команду `/reload` (если поддерживается)

## Дополнительные настройки

### Настройка поиска построек
```yaml
worlds:
  search:
    max-radius: 15  # Увеличить радиус поиска
    indicator-blocks:
      - "CHEST"
      - "CRAFTING_TABLE"
      - "FURNACE"
      - "TORCH"
      - "GLOWSTONE"
      - "DOOR"
      - "BED"
      - "SIGN"
      - "ANVIL"
      - "ENCHANTING_TABLE"
```

Добавьте больше блоков в список, если ваши постройки используют другие материалы.
