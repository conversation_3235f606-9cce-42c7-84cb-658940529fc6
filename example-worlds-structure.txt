Пример структуры папки archive-worlds:

server/
├── plugins/
│   └── ArchivePlugin-1.0.0.jar
├── archive-worlds/                    <- Эта папка создается автоматически
│   ├── survival_world/                <- Ваш первый мир
│   │   ├── level.dat                  <- Обязательный файл
│   │   ├── level.dat_old
│   │   ├── region/
│   │   │   ├── r.0.0.mca
│   │   │   └── ...
│   │   ├── data/
│   │   ├── playerdata/
│   │   └── ...
│   ├── creative_world/                <- Ваш второй мир
│   │   ├── level.dat                  <- Обязательный файл
│   │   ├── region/
│   │   └── ...
│   ├── adventure_map/                 <- Ваш третий мир
│   │   ├── level.dat                  <- Обязательный файл
│   │   ├── region/
│   │   └── ...
│   └── ...

ВАЖНО:
1. Каждая папка мира ДОЛЖНА содержать файл level.dat
2. Название папки будет отображаться в меню как название мира
3. Плагин автоматически найдет все корректные миры в этой папке
4. Миры будут скопированы в основную папку сервера при первой загрузке

Как добавить новый мир:
1. Поместите папку с миром в archive-worlds/
2. Убедитесь что в папке есть level.dat
3. Перезапустите сервер или используйте команду /archiveworlds
