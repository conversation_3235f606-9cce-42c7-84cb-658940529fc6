# ArchivePlugin Configuration

# Settings for the world selector
world-selector:
  # Give compass to players on join
  give-compass-on-join: true
  
  # Compass item settings
  compass:
    name: "Archive World Selector"
    lore:
      - "§7Right-click to open world selector"
      - "§8Choose from available archive worlds"
    glowing: true

# GUI Settings
gui:
  title: "Archive Worlds"
  size: 27 # Single chest size
  items-per-page: 21

# World settings
worlds:
  # Directory name for archive worlds (relative to server root)
  directory: "archive-worlds"
  
  # Auto-load worlds on server start
  auto-load: true
  
  # Copy worlds to server directory before loading
  copy-to-server: true

# Messages
messages:
  no-permission: "§cYou don't have permission to use this!"
  teleported: "§aTeleported to world: %world%"
  teleport-failed: "§cFailed to teleport to world: %world%"
  compass-received: "§aYou received the Archive World Selector!"
  players-only: "§cThis command can only be used by players!"
