# 🎯 Финальная настройка ArchivePlugin

## ✅ Готовый плагин: `ArchivePlugin.jar`

Все лишние файлы удалены, остался только рабочий плагин!

## 🚀 Установка:

1. **Скопируйте** `ArchivePlugin.jar` в папку `plugins` вашего сервера
2. **Перезапустите** сервер
3. **Готово!** Плагин работает

## 🎮 Команды для решения проблемы с телепортацией:

### Основные команды:
```bash
/archiveworlds                # Открыть меню миров
/archiveworlds setspawn       # Установить spawn точку для текущего мира  
/archiveworlds listspawns     # Посмотреть все spawn точки
```

### Алиасы (короткие команды):
```bash
/aw                          # Открыть меню миров
/aw setspawn                 # Установить spawn точку
/aw listspawns               # Посмотреть spawn точки
```

## 📋 Пошаговое решение проблемы:

### Шаг 1: Зайдите в проблемный мир
```bash
/archiveworlds
# Выберите мир где игроки попадают не туда
```

### Шаг 2: Найдите правильное место
- Найдите место где должны появляться игроки
- Встаньте в это место

### Шаг 3: Установите spawn точку
```bash
/archiveworlds setspawn
```

### Шаг 4: Проверьте результат
```bash
/archiveworlds listspawns
# Увидите список всех установленных spawn точек
```

### Шаг 5: Протестируйте
- Попробуйте телепортироваться в мир снова
- Теперь вы должны попасть в правильное место!

## 🔑 Права доступа:

- **Установка spawn точек:** только админы (OP или `archiveplugin.admin`)
- **Телепортация в миры:** все игроки (`archiveplugin.use`)

## 💾 Где сохраняются настройки:

**Spawn точки:** `plugins/ArchivePlugin/config.yml`
```yaml
custom-spawns:
  world_name:
    x: 123.5
    y: 64.0
    z: 456.5
    yaw: 180.0
    pitch: 0.0
```

**Основные настройки:** `plugins/ArchivePlugin/config.yml`

## 🔧 Для разработчиков:

**API файлы для компиляции:**
- `libs/paper-api.jar` - Paper API (2.3 MB)
- `libs/adventure-api.jar` - Adventure API (396 KB)
- `libs/examination-api.jar` - Examination API (23 KB)

**Компиляция:**
```bash
javac -cp "libs/paper-api.jar;libs/adventure-api.jar;libs/examination-api.jar" -d "build/classes" src/main/java/com/archiveplugin/*.java
```

## 🎊 Готово!

Теперь у вас есть:
- ✅ Рабочий плагин `ArchivePlugin.jar`
- ✅ Команды для установки spawn точек
- ✅ API файлы для разработки
- ✅ Решение проблемы с телепортацией

**Проблема с неправильными координатами телепортации полностью решена!** 🎉
