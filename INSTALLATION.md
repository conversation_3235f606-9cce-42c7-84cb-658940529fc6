# Инструкция по установке ArchivePlugin

## Быстрый старт

1. **Скачайте готовый JAR файл** (если доступен) или соберите плагин самостоятельно
2. **Поместите JAR файл** в папку `plugins` вашего Paper сервера
3. **Запустите сервер** - плагин создаст папку `archive-worlds`
4. **Добавьте миры** в папку `archive-worlds`
5. **Перезапустите сервер** или используйте команду `/archiveworlds`

## Подробная установка

### Шаг 1: Требования

- **Paper/Spigot сервер** версии 1.21.1
- **Java 21+** (проверьте: `java -version`)
- **Maven** (для сборки из исходников)

### Шаг 2: Сборка плагина

#### Вариант A: С Maven (рекомендуется)

1. Установите Maven:
   - Скачайте с https://maven.apache.org/download.cgi
   - Распакуйте в папку (например, `C:\apache-maven-3.9.6`)
   - Добавьте `C:\apache-maven-3.9.6\bin` в PATH
   - Перезапустите командную строку

2. Соберите плагин:
   ```bash
   cd "Archive Plugin"
   mvn clean package
   ```
   
   Или используйте готовый скрипт:
   ```bash
   build.bat
   ```

#### Вариант B: Без Maven (ручная сборка)

1. Скачайте Paper API:
   - Создайте папку `libs`
   - Скачайте `paper-api-1.21.1-R0.1-SNAPSHOT.jar` с https://repo.papermc.io/
   - Поместите JAR в папку `libs`

2. Запустите ручную сборку:
   ```bash
   build-manual.bat
   ```

### Шаг 3: Установка на сервер

1. Скопируйте `ArchivePlugin-1.0.0.jar` в папку `plugins` сервера
2. Запустите сервер
3. Плагин создаст папку `archive-worlds` рядом с папкой `plugins`

### Шаг 4: Добавление миров

1. Поместите папки с мирами в `archive-worlds/`
2. Убедитесь, что каждый мир содержит файл `level.dat`
3. Перезапустите сервер

Пример структуры:
```
server/
├── plugins/
│   └── ArchivePlugin-1.0.0.jar
├── archive-worlds/
│   ├── survival_world/
│   │   ├── level.dat
│   │   ├── region/
│   │   └── ...
│   ├── creative_world/
│   │   ├── level.dat
│   │   ├── region/
│   │   └── ...
│   └── ...
```

## Использование

### Для игроков

1. **Получение компаса**: При входе на сервер вы получите светящийся компас
2. **Открытие меню**: ПКМ с компасом в руке
3. **Выбор мира**: Кликните на блок травы с названием мира
4. **Навигация**: Стрелки для смены страниц, барьер для закрытия

### Команды

- `/archiveworlds` - Открыть меню выбора миров
- `/aw` - Короткий алиас
- `/worlds` - Альтернативный алиас

### Права доступа

- `archiveplugin.use` - Использование селектора (по умолчанию: все)
- `archiveplugin.admin` - Административные права (по умолчанию: операторы)

## Решение проблем

### Плагин не загружается
- Проверьте версию сервера (должен быть Paper/Spigot 1.21.1)
- Проверьте версию Java (должна быть 21+)
- Проверьте логи сервера на ошибки

### Миры не отображаются
- Убедитесь, что в папке мира есть файл `level.dat`
- Проверьте права доступа к папке `archive-worlds`
- Перезапустите сервер после добавления новых миров

### Компас не работает
- Проверьте права доступа `archiveplugin.use`
- Убедитесь, что у вас правильный компас (светящийся с названием)
- Попробуйте команду `/archiveworlds`

### Телепортация не работает
- Проверьте, что мир корректно загружен
- Проверьте логи сервера на ошибки загрузки мира
- Убедитесь, что файлы мира не повреждены

## Поддержка

Если проблемы не решаются:
1. Проверьте логи сервера (`logs/latest.log`)
2. Убедитесь в корректности структуры миров
3. Проверьте права доступа к файлам и папкам
