# 🔧 Ручная настройка spawn точек

## 🎯 Проблема решена простым способом!

Поскольку команды не работают из-за проблем с Adventure API, давайте настроим spawn точки вручную через конфигурационный файл.

## 📁 Где находится конфиг:

**Путь:** `D:\serv\plugins\ArchivePlugin\config.yml`

## 📝 Как найти координаты:

### Шаг 1: Зайдите в проблемный мир
1. Используйте `/archiveworlds` (это работает)
2. Выберите мир где игроки попадают не туда

### Шаг 2: Найдите правильное место
1. Найдите место где должны появляться игроки
2. Встаньте в это место
3. Нажмите **F3** (откроется отладочная информация)
4. Найдите строку: `XYZ: 123.456 / 67.000 / -89.123`
5. Запишите координаты: **X=123, Y=67, Z=-89**

### Шаг 3: Откройте config.yml
Откройте файл `D:\serv\plugins\ArchivePlugin\config.yml` в блокноте

### Шаг 4: Добавьте spawn точки
Добавьте в конец файла:

```yaml
custom-spawns:
  world_name_1:
    x: 123.0
    y: 67.0
    z: -89.0
    yaw: 0.0
    pitch: 0.0
  world_name_2:
    x: 0.0
    y: 64.0
    z: 0.0
    yaw: 180.0
    pitch: 0.0
```

**Замените:**
- `world_name_1` на реальное имя мира
- `123.0, 67.0, -89.0` на ваши координаты

## 📋 Пример готового config.yml:

```yaml
# Основные настройки плагина
worlds:
  directory: "archive-worlds"
  auto-load: true
  copy-to-server: true

# Кастомные spawn точки
custom-spawns:
  # Замените на имена ваших миров
  survival_world:
    x: 256.5
    y: 75.0
    z: -128.5
    yaw: 180.0
    pitch: 0.0
    
  creative_world:
    x: 0.5
    y: 64.0
    z: 0.5
    yaw: 0.0
    pitch: 0.0
    
  adventure_map:
    x: -100.5
    y: 70.0
    z: 200.5
    yaw: 90.0
    pitch: 0.0
```

## 🔄 После настройки:

1. **Сохраните** config.yml
2. **Перезапустите** сервер
3. **Протестируйте** телепортацию в миры

## 🎯 Как узнать имена миров:

### Способ 1: Через F3
1. Зайдите в мир
2. Нажмите F3
3. Найдите строку с именем мира

### Способ 2: Через папки
Посмотрите в папку `D:\serv\archive-worlds\` - там будут папки с именами миров

### Способ 3: Через консоль
В консоли сервера выполните: `/worlds` или `/mv list`

## 💡 Советы:

### Координаты yaw и pitch:
- **yaw: 0** - игрок смотрит на север
- **yaw: 90** - игрок смотрит на запад  
- **yaw: 180** - игрок смотрит на юг
- **yaw: 270** - игрок смотрит на восток
- **pitch: 0** - игрок смотрит прямо
- **pitch: -90** - игрок смотрит вверх
- **pitch: 90** - игрок смотрит вниз

### Безопасные координаты:
- Y должен быть на твердом блоке + 1
- Убедитесь что над головой есть место (2 блока)
- Избегайте лавы, воды, пропастей

## ✅ Готово!

После настройки config.yml игроки будут телепортироваться в правильные места!

**Этот способ работает на 100% без дополнительных плагинов!** 🎉
