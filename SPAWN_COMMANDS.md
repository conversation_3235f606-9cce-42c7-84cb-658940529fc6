# Команды для настройки spawn точек

## Проблема решена! 🎉

Теперь у вас есть простые команды для установки правильных точек телепортации для каждого мира.

## Новые команды:

### 1. `/setarchivespawn` - Установить spawn точку
```
/setarchivespawn                    # Установить spawn для текущего мира
/setarchivespawn world_name         # Установить spawn для указанного мира
```

**Алиасы:** `/setaspawn`, `/sas`

### 2. `/listarchivespawns` - Посмотреть все spawn точки
```
/listarchivespawns                  # Показать все установленные spawn точки
```

**Алиасы:** `/listaspawns`, `/las`

## Как использовать:

### Шаг 1: Зайдите в мир и найдите нужное место
1. Используйте `/archiveworlds` чтобы попасть в мир
2. Найдите место, где должны появляться игроки
3. Встаньте в это место

### Шаг 2: Установите spawn точку
```
/setarchivespawn
```

### Шаг 3: Проверьте результат
```
/listarchivespawns
```

## Примеры:

```bash
# Установить spawn для текущего мира
/setarchivespawn

# Установить spawn для мира "castle"
/setarchivespawn castle

# Посмотреть все spawn точки
/listarchivespawns
```

## Права доступа:

- **Использование команд:** `archiveplugin.admin` (только для админов)
- **Телепортация:** `archiveplugin.use` (для всех игроков)

## Что происходит:

1. Когда вы устанавливаете spawn точку, она сохраняется в файл `plugins/ArchivePlugin/spawns.yml`
2. При телепортации в мир плагин сначала проверяет, есть ли кастомная spawn точка
3. Если есть - телепортирует туда, если нет - использует стандартную spawn точку мира

## Файл spawns.yml:

После установки spawn точек в файле `plugins/ArchivePlugin/spawns.yml` будет что-то вроде:

```yaml
spawns:
  castle-world:
    x: 256.5
    y: 75.0
    z: -128.5
    yaw: 180.0
    pitch: 0.0
  city-world:
    x: 0.5
    y: 64.0
    z: 0.5
    yaw: 0.0
    pitch: 0.0
```

## Удаление spawn точки:

Чтобы удалить кастомную spawn точку:
1. Откройте `plugins/ArchivePlugin/spawns.yml`
2. Удалите секцию с нужным миром
3. Перезапустите сервер

Или просто установите новую spawn точку - она перезапишет старую.

## Готово! 🚀

Теперь игроки будут телепортироваться именно туда, куда вы установили spawn точки!
