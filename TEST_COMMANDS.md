# 🧪 Тестирование команд ArchivePlugin

## 🔍 Проблема: аргументы не работают

Команды `/archiveworlds setspawn` и `/archiveworlds listspawns` просто открывают меню вместо выполнения действий.

## 🎯 Попробуйте отдельные команды:

### Вариант 1: Отдельные команды (должны работать)
```bash
/setarchivespawn        # Установить spawn точку
/listarchivespawns      # Посмотреть spawn точки
```

### Вариант 2: Алиасы
```bash
/setaspawn             # Установить spawn точку
/sas                   # Установить spawn точку (короткий алиас)

/listaspawns           # Посмотреть spawn точки  
/las                   # Посмотреть spawn точки (короткий алиас)
```

## 📋 Пошаговое тестирование:

### Шаг 1: Проверьте основную команду
```bash
/archiveworlds
```
**Ожидаемый результат:** Открывается меню миров ✅

### Шаг 2: Попробуйте отдельную команду установки spawn
```bash
/setarchivespawn
```
**Ожидаемый результат:** 
- Если вы админ: "Spawn point set for world..."
- Если не админ: "You don't have permission..."

### Шаг 3: Попробуйте команду просмотра spawn точек
```bash
/listarchivespawns
```
**Ожидаемый результат:**
- Если есть spawn точки: список координат
- Если нет: "No custom spawn points set"

### Шаг 4: Попробуйте алиасы
```bash
/sas                   # Установить spawn
/las                   # Посмотреть spawn точки
```

## 🔧 Если команды не работают:

### Проверьте права доступа:
```bash
/op your_username      # Дать себе права админа
```

### Проверьте загрузку плагина:
```bash
/plugins               # Должен быть зеленый ArchivePlugin
```

### Проверьте помощь по командам:
```bash
/help setarchivespawn
/help listarchivespawns
```

## 🎯 Ожидаемое поведение:

### При установке spawn точки:
```
> /setarchivespawn
Spawn point set for world 'world_name' at:
X: 123, Y: 64, Z: 456
```

### При просмотре spawn точек:
```
> /listarchivespawns
Custom Spawn Points:
---------------------
world1:
  X: 100, Y: 70, Z: -50
world2:
  X: 0, Y: 64, Z: 0
```

## 📝 Результаты тестирования:

**Запишите результаты:**
- [ ] `/archiveworlds` - работает/не работает
- [ ] `/setarchivespawn` - работает/не работает  
- [ ] `/listarchivespawns` - работает/не работает
- [ ] `/sas` - работает/не работает
- [ ] `/las` - работает/не работает

## 🔄 Если ничего не работает:

Возможно, нужно пересобрать плагин с исправленным кодом. В этом случае мы создадим новую версию без Adventure API.
