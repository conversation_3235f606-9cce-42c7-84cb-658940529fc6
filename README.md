# ArchivePlugin

Плагин для Paper Minecraft 1.21.1, который позволяет игрокам выбирать и телепортироваться между различными мирами через интуитивный GUI интерфейс с компасом.

## Особенности

- 🧭 **Компас для выбора миров** - Игроки получают специальный компас при входе на сервер
- 🗂️ **GUI меню** - Удобный интерфейс размером с одиночный сундук (27 слотов)
- 📄 **Пагинация** - Поддержка большого количества миров с навигацией по страницам
- 🌍 **Автоматическая загрузка миров** - Миры автоматически загружаются из папки `archive-worlds`
- 🔧 **Простая установка** - Просто поместите миры в папку и перезапустите сервер

## Установка

1. Скомпилируйте плагин с помощью Maven:
   ```bash
   mvn clean package
   ```

2. Поместите полученный JAR файл в папку `plugins` вашего Paper сервера

3. Запустите сервер - плагин автоматически создаст папку `archive-worlds`

4. Поместите ваши миры Minecraft в папку `archive-worlds`

5. Перезапустите сервер или используйте команду для перезагрузки миров

## Структура папки миров

```
server/
├── plugins/
│   └── ArchivePlugin.jar
├── archive-worlds/
│   ├── world1/
│   │   ├── level.dat
│   │   ├── region/
│   │   └── ...
│   ├── world2/
│   │   ├── level.dat
│   │   ├── region/
│   │   └── ...
│   └── ...
```

## Использование

### Для игроков

1. **Получение компаса**: При входе на сервер вы автоматически получите светящийся компас "Archive World Selector"

2. **Открытие меню**: Нажмите правой кнопкой мыши с компасом в руке

3. **Выбор мира**: Кликните на блок травы с названием мира для телепортации

4. **Навигация**: 
   - Используйте стрелки для перехода между страницами
   - Красный барьер для закрытия меню
   - Книга показывает текущую страницу

### Команды

- `/archiveworlds` (алиасы: `/aw`, `/worlds`) - Открыть меню выбора миров

### Права доступа

- `archiveplugin.use` - Разрешает использование селектора миров (по умолчанию: true)
- `archiveplugin.admin` - Административные права (по умолчанию: op)

## Требования

- Paper/Spigot 1.21.1
- Java 21+
- Maven (для компиляции)

## Компиляция

### Установка Maven (если не установлен)

1. Скачайте Maven с официального сайта: https://maven.apache.org/download.cgi
2. Распакуйте архив в папку (например, `C:\apache-maven-3.9.6`)
3. Добавьте `C:\apache-maven-3.9.6\bin` в переменную PATH
4. Перезапустите командную строку и проверьте: `mvn -version`

### Сборка плагина

```bash
cd ArchivePlugin
mvn clean package
```

Или используйте готовый скрипт:
```bash
build.bat
```

Скомпилированный JAR файл будет находиться в папке `target/`.

## Поддержка

Если у вас возникли проблемы:

1. Убедитесь, что ваши миры имеют корректную структуру (содержат `level.dat`)
2. Проверьте логи сервера на наличие ошибок
3. Убедитесь, что у игроков есть права `archiveplugin.use`

## Лицензия

Этот проект распространяется под лицензией MIT.
