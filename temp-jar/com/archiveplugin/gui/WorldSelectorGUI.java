package com.archiveplugin.gui;

import com.archiveplugin.ArchivePlugin;
import com.archiveplugin.utils.ItemBuilder;
import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.format.NamedTextColor;
import net.kyori.adventure.text.format.TextDecoration;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class WorldSelectorGUI implements Listener {
    
    private final ArchivePlugin plugin;
    private final Map<Player, Integer> playerPages;
    private static final int ITEMS_PER_PAGE = 21; // 3 rows of 7 items (leaving space for navigation)
    private static final int GUI_SIZE = 27; // Single chest size
    
    public WorldSelectorGUI(ArchivePlugin plugin) {
        this.plugin = plugin;
        this.playerPages = new HashMap<>();
        Bukkit.getPluginManager().registerEvents(this, plugin);
    }
    
    public void openWorldSelector(Player player) {
        openWorldSelector(player, 0);
    }
    
    public void openWorldSelector(Player player, int page) {
        List<String> worlds = plugin.getWorldManager().getAvailableWorlds();
        
        Inventory gui = Bukkit.createInventory(null, GUI_SIZE, 
            Component.text("Archive Worlds - Page " + (page + 1))
                .color(NamedTextColor.DARK_PURPLE)
                .decoration(TextDecoration.BOLD, true));
        
        playerPages.put(player, page);
        
        // Calculate pagination
        int startIndex = page * ITEMS_PER_PAGE;
        int endIndex = Math.min(startIndex + ITEMS_PER_PAGE, worlds.size());
        
        // Add world items
        int slot = 0;
        for (int i = startIndex; i < endIndex; i++) {
            String worldName = worlds.get(i);
            
            ItemStack worldItem = new ItemBuilder(Material.GRASS_BLOCK)
                .setDisplayName(Component.text(worldName)
                    .color(NamedTextColor.GREEN)
                    .decoration(TextDecoration.BOLD, true))
                .addLoreLine("§7Click to teleport to this world")
                .addLoreLine("§8World: " + worldName)
                .build();
            
            gui.setItem(slot, worldItem);
            slot++;
            
            // Skip to next row if we've filled 7 items
            if (slot % 9 == 7) {
                slot += 2; // Skip to start of next row
            }
        }
        
        // Add navigation items
        addNavigationItems(gui, page, worlds.size());
        
        player.openInventory(gui);
    }
    
    private void addNavigationItems(Inventory gui, int currentPage, int totalWorlds) {
        int totalPages = (int) Math.ceil((double) totalWorlds / ITEMS_PER_PAGE);
        
        // Previous page button
        if (currentPage > 0) {
            ItemStack prevButton = new ItemBuilder(Material.ARROW)
                .setDisplayName(Component.text("Previous Page")
                    .color(NamedTextColor.YELLOW)
                    .decoration(TextDecoration.BOLD, true))
                .addLoreLine("§7Click to go to page " + currentPage)
                .build();
            gui.setItem(18, prevButton);
        }
        
        // Close button
        ItemStack closeButton = new ItemBuilder(Material.BARRIER)
            .setDisplayName(Component.text("Close")
                .color(NamedTextColor.RED)
                .decoration(TextDecoration.BOLD, true))
            .addLoreLine("§7Click to close this menu")
            .build();
        gui.setItem(22, closeButton);
        
        // Next page button
        if (currentPage < totalPages - 1) {
            ItemStack nextButton = new ItemBuilder(Material.ARROW)
                .setDisplayName(Component.text("Next Page")
                    .color(NamedTextColor.YELLOW)
                    .decoration(TextDecoration.BOLD, true))
                .addLoreLine("§7Click to go to page " + (currentPage + 2))
                .build();
            gui.setItem(26, nextButton);
        }
        
        // Page info
        ItemStack pageInfo = new ItemBuilder(Material.BOOK)
            .setDisplayName(Component.text("Page " + (currentPage + 1) + "/" + Math.max(1, totalPages))
                .color(NamedTextColor.AQUA)
                .decoration(TextDecoration.BOLD, true))
            .addLoreLine("§7Total worlds: " + totalWorlds)
            .build();
        gui.setItem(13, pageInfo);
    }
    
    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) {
        if (!(event.getWhoClicked() instanceof Player player)) return;

        // Check if this is our GUI by checking if the title starts with "Archive Worlds"
        Component title = event.getView().title();
        if (title instanceof net.kyori.adventure.text.TextComponent textTitle) {
            if (!textTitle.content().startsWith("Archive Worlds")) {
                return;
            }
        } else {
            return;
        }
        
        event.setCancelled(true);
        
        ItemStack clickedItem = event.getCurrentItem();
        if (clickedItem == null || clickedItem.getType() == Material.AIR) return;
        
        int slot = event.getSlot();
        
        // Handle navigation
        if (slot == 18 && clickedItem.getType() == Material.ARROW) {
            // Previous page
            int currentPage = playerPages.getOrDefault(player, 0);
            if (currentPage > 0) {
                openWorldSelector(player, currentPage - 1);
            }
            return;
        }
        
        if (slot == 26 && clickedItem.getType() == Material.ARROW) {
            // Next page
            int currentPage = playerPages.getOrDefault(player, 0);
            List<String> worlds = plugin.getWorldManager().getAvailableWorlds();
            int totalPages = (int) Math.ceil((double) worlds.size() / ITEMS_PER_PAGE);
            if (currentPage < totalPages - 1) {
                openWorldSelector(player, currentPage + 1);
            }
            return;
        }
        
        if (slot == 22 && clickedItem.getType() == Material.BARRIER) {
            // Close
            player.closeInventory();
            playerPages.remove(player);
            return;
        }
        
        // Handle world selection
        if (clickedItem.getType() == Material.GRASS_BLOCK) {
            Component displayName = clickedItem.getItemMeta().displayName();
            if (displayName != null) {
                String worldName = ((net.kyori.adventure.text.TextComponent) displayName).content();
                
                player.closeInventory();
                playerPages.remove(player);
                
                if (plugin.getWorldManager().teleportPlayerToWorld(player, worldName)) {
                    player.sendMessage(Component.text("Teleported to world: " + worldName)
                        .color(NamedTextColor.GREEN));
                } else {
                    player.sendMessage(Component.text("Failed to teleport to world: " + worldName)
                        .color(NamedTextColor.RED));
                }
            }
        }
    }
}
