package com.archiveplugin.commands;

import com.archiveplugin.ArchivePlugin;
import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.format.NamedTextColor;
import org.bukkit.Location;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;

import java.util.Map;

public class ListSpawnsCommand implements CommandExecutor {
    
    private final ArchivePlugin plugin;

    public ListSpawnsCommand(ArchivePlugin plugin) {
        this.plugin = plugin;
    }
    
    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!(sender instanceof Player player)) {
            sender.sendMessage(Component.text("This command can only be used by players!")
                .color(NamedTextColor.RED));
            return true;
        }
        
        if (!player.hasPermission("archiveplugin.admin")) {
            player.sendMessage(Component.text("You don't have permission to use this command!")
                .color(NamedTextColor.RED));
            return true;
        }
        
        Map<String, Location> customSpawns = plugin.getSpawnManager().getCustomSpawns();
        
        if (customSpawns.isEmpty()) {
            player.sendMessage(Component.text("No custom spawn points set.")
                .color(NamedTextColor.YELLOW));
            player.sendMessage(Component.text("Use /setarchivespawn to set spawn points.")
                .color(NamedTextColor.GRAY));
            return true;
        }
        
        player.sendMessage(Component.text("Custom Spawn Points:")
            .color(NamedTextColor.GREEN));
        player.sendMessage(Component.text("─────────────────────")
            .color(NamedTextColor.GRAY));
        
        for (Map.Entry<String, Location> entry : customSpawns.entrySet()) {
            String worldName = entry.getKey();
            Location loc = entry.getValue();
            
            player.sendMessage(Component.text("• " + worldName + ":")
                .color(NamedTextColor.YELLOW));
            player.sendMessage(Component.text("  X: " + (int)loc.getX() + ", Y: " + (int)loc.getY() + ", Z: " + (int)loc.getZ())
                .color(NamedTextColor.GRAY));
        }
        
        return true;
    }
}
