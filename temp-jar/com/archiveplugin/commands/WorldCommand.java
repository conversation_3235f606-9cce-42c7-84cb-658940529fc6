package com.archiveplugin.commands;

import com.archiveplugin.ArchivePlugin;
import com.archiveplugin.gui.WorldSelectorGUI;
import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.format.NamedTextColor;
import org.bukkit.Location;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.entity.Player;

public class WorldCommand implements CommandExecutor {
    
    private final ArchivePlugin plugin;

    public WorldCommand(ArchivePlugin plugin) {
        this.plugin = plugin;
    }
    
    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!(sender instanceof Player player)) {
            sender.sendMessage(Component.text("This command can only be used by players!")
                .color(NamedTextColor.RED));
            return true;
        }

        // Check for spawn setting commands
        if (args.length > 0) {
            if (args[0].equalsIgnoreCase("setspawn")) {
                return handleSetSpawn(player, args);
            } else if (args[0].equalsIgnoreCase("listspawns")) {
                return handleListSpawns(player);
            }
        }

        if (!player.hasPermission("archiveplugin.use")) {
            player.sendMessage(Component.text("You don't have permission to use this command!")
                .color(NamedTextColor.RED));
            return true;
        }

        plugin.getWorldSelectorGUI().openWorldSelector(player);
        return true;
    }

    private boolean handleSetSpawn(Player player, String[] args) {
        if (!player.hasPermission("archiveplugin.admin") && !player.isOp()) {
            player.sendMessage(Component.text("You don't have permission to set spawn points!")
                .color(NamedTextColor.RED));
            return true;
        }

        Location loc = player.getLocation();
        String worldName = loc.getWorld().getName();

        // Save to config
        FileConfiguration config = plugin.getConfig();
        String path = "custom-spawns." + worldName;
        config.set(path + ".x", loc.getX());
        config.set(path + ".y", loc.getY());
        config.set(path + ".z", loc.getZ());
        config.set(path + ".yaw", loc.getYaw());
        config.set(path + ".pitch", loc.getPitch());

        plugin.saveConfig();

        player.sendMessage(Component.text("✅ Spawn point set for world '" + worldName + "' at:")
            .color(NamedTextColor.GREEN));
        player.sendMessage(Component.text("📍 X: " + (int)loc.getX() + ", Y: " + (int)loc.getY() + ", Z: " + (int)loc.getZ())
            .color(NamedTextColor.YELLOW));

        plugin.getLogger().info("Player " + player.getName() + " set spawn for world " + worldName +
                               " at " + (int)loc.getX() + ", " + (int)loc.getY() + ", " + (int)loc.getZ());

        return true;
    }

    private boolean handleListSpawns(Player player) {
        if (!player.hasPermission("archiveplugin.admin") && !player.isOp()) {
            player.sendMessage(Component.text("You don't have permission to list spawn points!")
                .color(NamedTextColor.RED));
            return true;
        }

        FileConfiguration config = plugin.getConfig();
        ConfigurationSection spawnsSection = config.getConfigurationSection("custom-spawns");

        if (spawnsSection == null || spawnsSection.getKeys(false).isEmpty()) {
            player.sendMessage(Component.text("❌ No custom spawn points set.")
                .color(NamedTextColor.YELLOW));
            player.sendMessage(Component.text("💡 Use /archiveworlds setspawn to set spawn points.")
                .color(NamedTextColor.GRAY));
            return true;
        }

        player.sendMessage(Component.text("📋 Custom Spawn Points:")
            .color(NamedTextColor.GREEN));
        player.sendMessage(Component.text("─────────────────────")
            .color(NamedTextColor.GRAY));

        for (String worldName : spawnsSection.getKeys(false)) {
            double x = config.getDouble("custom-spawns." + worldName + ".x");
            double y = config.getDouble("custom-spawns." + worldName + ".y");
            double z = config.getDouble("custom-spawns." + worldName + ".z");

            player.sendMessage(Component.text("🌍 " + worldName + ":")
                .color(NamedTextColor.YELLOW));
            player.sendMessage(Component.text("   📍 X: " + (int)x + ", Y: " + (int)y + ", Z: " + (int)z)
                .color(NamedTextColor.GRAY));
        }

        return true;
    }
}
