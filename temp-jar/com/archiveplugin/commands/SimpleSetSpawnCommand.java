package com.archiveplugin.commands;

import com.archiveplugin.ArchivePlugin;
import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.format.NamedTextColor;
import org.bukkit.Location;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.entity.Player;

import java.io.File;

public class SimpleSetSpawnCommand implements CommandExecutor {
    
    private final ArchivePlugin plugin;

    public SimpleSetSpawnCommand(ArchivePlugin plugin) {
        this.plugin = plugin;
    }
    
    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!(sender instanceof Player player)) {
            sender.sendMessage(Component.text("This command can only be used by players!")
                .color(NamedTextColor.RED));
            return true;
        }
        
        if (!player.hasPermission("archiveplugin.admin") && !player.isOp()) {
            player.sendMessage(Component.text("You don't have permission to use this command!")
                .color(NamedTextColor.RED));
            return true;
        }
        
        Location loc = player.getLocation();
        String worldName = loc.getWorld().getName();
        
        // Save to config
        FileConfiguration config = plugin.getConfig();
        String path = "custom-spawns." + worldName;
        config.set(path + ".x", loc.getX());
        config.set(path + ".y", loc.getY());
        config.set(path + ".z", loc.getZ());
        config.set(path + ".yaw", loc.getYaw());
        config.set(path + ".pitch", loc.getPitch());
        
        plugin.saveConfig();
        
        player.sendMessage(Component.text("Spawn point set for world '" + worldName + "' at:")
            .color(NamedTextColor.GREEN));
        player.sendMessage(Component.text("X: " + (int)loc.getX() + ", Y: " + (int)loc.getY() + ", Z: " + (int)loc.getZ())
            .color(NamedTextColor.YELLOW));
        
        plugin.getLogger().info("Player " + player.getName() + " set spawn for world " + worldName + 
                               " at " + (int)loc.getX() + ", " + (int)loc.getY() + ", " + (int)loc.getZ());
        
        return true;
    }
}
