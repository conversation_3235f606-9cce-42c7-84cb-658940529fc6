package com.archiveplugin.managers;

import com.archiveplugin.ArchivePlugin;
import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.World;
import org.bukkit.WorldCreator;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.entity.Player;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Logger;

public class ConfigWorldManager {
    
    private final ArchivePlugin plugin;
    private final Logger logger;
    private final File worldsDirectory;
    
    public ConfigWorldManager(ArchivePlugin plugin) {
        this.plugin = plugin;
        this.logger = plugin.getLogger();
        this.worldsDirectory = new File(plugin.getDataFolder().getParentFile(), "archive-worlds");
        
        loadAvailableWorlds();
    }
    
    public List<String> getAvailableWorlds() {
        List<String> worlds = new ArrayList<>();
        
        if (!worldsDirectory.exists()) {
            return worlds;
        }
        
        File[] worldFolders = worldsDirectory.listFiles(File::isDirectory);
        if (worldFolders != null) {
            for (File worldFolder : worldFolders) {
                // Check if it's a valid Minecraft world (has level.dat)
                File levelDat = new File(worldFolder, "level.dat");
                if (levelDat.exists()) {
                    worlds.add(worldFolder.getName());
                }
            }
        }
        
        return worlds;
    }
    
    public void loadAvailableWorlds() {
        List<String> worlds = getAvailableWorlds();
        
        for (String worldName : worlds) {
            try {
                if (Bukkit.getWorld(worldName) == null) {
                    // Copy world to server directory if needed
                    File serverWorldDir = new File(Bukkit.getWorldContainer(), worldName);
                    File archiveWorldDir = new File(worldsDirectory, worldName);
                    
                    if (!serverWorldDir.exists() && archiveWorldDir.exists()) {
                        copyWorld(archiveWorldDir, serverWorldDir);
                    }
                    
                    // Load the world
                    WorldCreator creator = new WorldCreator(worldName);
                    World world = creator.createWorld();
                    
                    if (world != null) {
                        logger.info("Loaded world: " + worldName);
                    }
                }
            } catch (Exception e) {
                logger.warning("Failed to load world: " + worldName + " - " + e.getMessage());
            }
        }
    }
    
    public boolean teleportPlayerToWorld(Player player, String worldName) {
        World world = Bukkit.getWorld(worldName);
        
        if (world == null) {
            // Try to load the world
            try {
                File serverWorldDir = new File(Bukkit.getWorldContainer(), worldName);
                File archiveWorldDir = new File(worldsDirectory, worldName);
                
                if (!serverWorldDir.exists() && archiveWorldDir.exists()) {
                    copyWorld(archiveWorldDir, serverWorldDir);
                }
                
                WorldCreator creator = new WorldCreator(worldName);
                world = creator.createWorld();
            } catch (Exception e) {
                logger.warning("Failed to load world for teleportation: " + worldName + " - " + e.getMessage());
                return false;
            }
        }
        
        if (world != null) {
            Location teleportLocation = getCustomSpawnLocation(worldName, world);
            player.teleport(teleportLocation);
            
            if (hasCustomSpawn(worldName)) {
                logger.info("Teleported " + player.getName() + " to custom spawn in " + worldName);
            } else {
                logger.info("Teleported " + player.getName() + " to default spawn in " + worldName);
            }
            
            return true;
        }
        
        return false;
    }
    
    private Location getCustomSpawnLocation(String worldName, World world) {
        FileConfiguration config = plugin.getConfig();
        String path = "custom-spawns." + worldName;
        
        if (config.contains(path)) {
            try {
                double x = config.getDouble(path + ".x");
                double y = config.getDouble(path + ".y");
                double z = config.getDouble(path + ".z");
                float yaw = (float) config.getDouble(path + ".yaw", 0);
                float pitch = (float) config.getDouble(path + ".pitch", 0);
                
                return new Location(world, x, y, z, yaw, pitch);
            } catch (Exception e) {
                logger.warning("Failed to load custom spawn for " + worldName + ": " + e.getMessage());
            }
        }
        
        return world.getSpawnLocation();
    }
    
    private boolean hasCustomSpawn(String worldName) {
        return plugin.getConfig().contains("custom-spawns." + worldName);
    }
    
    private void copyWorld(File source, File target) {
        try {
            if (!target.exists()) {
                target.mkdirs();
            }
            
            copyDirectory(source.toPath(), target.toPath());
        } catch (Exception e) {
            logger.warning("Failed to copy world from " + source.getName() + " to " + target.getName() + ": " + e.getMessage());
        }
    }
    
    private void copyDirectory(Path source, Path target) throws IOException {
        Files.walk(source).forEach(sourcePath -> {
            try {
                Path targetPath = target.resolve(source.relativize(sourcePath));
                if (Files.isDirectory(sourcePath)) {
                    Files.createDirectories(targetPath);
                } else {
                    Files.copy(sourcePath, targetPath, StandardCopyOption.REPLACE_EXISTING);
                }
            } catch (IOException e) {
                logger.warning("Failed to copy file: " + sourcePath + " - " + e.getMessage());
            }
        });
    }
}
