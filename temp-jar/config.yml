# ArchivePlugin Configuration

# Settings for the world selector
world-selector:
  # Give compass to players on join
  give-compass-on-join: true
  
  # Compass item settings
  compass:
    name: "Archive World Selector"
    lore:
      - "§7Right-click to open world selector"
      - "§8Choose from available archive worlds"
    glowing: true

# GUI Settings
gui:
  title: "Archive Worlds"
  size: 27 # Single chest size
  items-per-page: 21

# World settings
worlds:
  # Directory name for archive worlds (relative to server root)
  directory: "archive-worlds"

  # Auto-load worlds on server start
  auto-load: true

  # Copy worlds to server directory before loading
  copy-to-server: true

  # Teleport strategy: "spawn", "highest", "search", "custom"
  # spawn - use world spawn point (default)
  # highest - find highest safe location near spawn
  # search - search for player-built structures
  # custom - use custom coordinates defined below
  teleport-strategy: "search"

  # Search settings for finding structures
  search:
    # Maximum radius to search (in chunks)
    max-radius: 10
    # Search for these block types as indicators of player activity
    indicator-blocks:
      - "CHEST"
      - "CRAFTING_TABLE"
      - "FURNACE"
      - "TORCH"
      - "DOOR"
      - "BED"
      - "SIGN"

  # Custom teleport coordinates for specific worlds
  # Format: world-name: {x: 0, y: 64, z: 0, yaw: 0, pitch: 0}
  custom-coordinates:
    # Example:
    # my-world: {x: 100, y: 70, z: -50, yaw: 90, pitch: 0}
    # another-world: {x: 0, y: 64, z: 0}

# Messages
messages:
  no-permission: "§cYou don't have permission to use this!"
  teleported: "§aTeleported to world: %world%"
  teleport-failed: "§cFailed to teleport to world: %world%"
  compass-received: "§aYou received the Archive World Selector!"
  players-only: "§cThis command can only be used by players!"
