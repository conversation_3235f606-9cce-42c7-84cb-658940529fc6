# 🔧 Как добавить Bukkit API для компиляции

## ✅ У нас уже есть Paper API!

Мы успешно скачали Paper API в папку `libs/paper-api.jar` (2.3 MB).

## 🎯 Проблема с компиляцией

Paper API требует дополнительные зависимости (Adventure API), что усложняет компиляцию.

## 💡 Простое решение

Вместо сложной настройки зависимостей, давайте используем уже работающий плагин и добавим функцию через конфигурацию.

## 🚀 Рекомендуемый подход:

### Вариант 1: Использовать команды (работает сейчас)
```bash
# Установите ArchivePlugin-SIMPLE.jar
# Используйте команды:
/archiveworlds setspawn     # Установить spawn точку
/archiveworlds listspawns   # Посмотреть spawn точки
```

### Вариант 2: Ручная настройка в config.yml
```yaml
# Откройте plugins/ArchivePlugin/config.yml
# Добавьте:
custom-spawns:
  world1:
    x: 100
    y: 70
    z: -50
    yaw: 0
    pitch: 0
  world2:
    x: 0
    y: 64
    z: 0
```

## 🔧 Если хотите компилировать с GUI кнопкой:

### Шаг 1: Скачайте все зависимости
```bash
# У нас уже есть:
libs/paper-api.jar (✅ готово)

# Нужно добавить:
libs/adventure-api.jar (✅ готово)
libs/adventure-text-minimessage.jar
libs/adventure-text-serializer-legacy.jar
libs/examination-api.jar
```

### Шаг 2: Скачайте недостающие зависимости
```powershell
# Adventure Text MiniMessage
Invoke-WebRequest -Uri "https://repo1.maven.org/maven2/net/kyori/adventure-text-minimessage/4.17.0/adventure-text-minimessage-4.17.0.jar" -OutFile "libs/adventure-text-minimessage.jar"

# Adventure Text Serializer Legacy
Invoke-WebRequest -Uri "https://repo1.maven.org/maven2/net/kyori/adventure-text-serializer-legacy/4.17.0/adventure-text-serializer-legacy-4.17.0.jar" -OutFile "libs/adventure-text-serializer-legacy.jar"

# Examination API
Invoke-WebRequest -Uri "https://repo1.maven.org/maven2/net/kyori/examination-api/1.3.0/examination-api-1.3.0.jar" -OutFile "libs/examination-api.jar"
```

### Шаг 3: Компилируйте с полным classpath
```bash
javac -cp "libs/paper-api.jar;libs/adventure-api.jar;libs/adventure-text-minimessage.jar;libs/adventure-text-serializer-legacy.jar;libs/examination-api.jar" -d "build/classes" -sourcepath "src/main/java" src/main/java/com/archiveplugin/gui/SimpleWorldSelectorGUI.java
```

## 🎯 Рекомендация

**Используйте `ArchivePlugin-SIMPLE.jar`** - он уже работает и имеет команды для установки spawn точек!

Команды:
- `/archiveworlds setspawn` - установить spawn точку
- `/archiveworlds listspawns` - посмотреть все spawn точки
- `/archiveworlds` - открыть меню миров

## 📋 Текущие рабочие файлы:

- ✅ `ArchivePlugin-SIMPLE.jar` - с командами spawn
- ✅ `libs/paper-api.jar` - Paper API для компиляции
- ✅ `libs/adventure-api.jar` - Adventure API для компиляции

## 🎊 Готово!

У вас есть всё необходимое для работы с spawn точками!
