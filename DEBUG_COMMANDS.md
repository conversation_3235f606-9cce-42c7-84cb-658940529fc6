# Отладка команд ArchivePlugin 2.1.0

## Если команда `/setarchivespawn` не работает:

### 1. Проверьте права доступа
```
/op your_username
```
Или убедитесь, что у вас есть право `archiveplugin.admin`

### 2. Проверьте, что плагин загружен
```
/plugins
```
Должен быть зеленый `ArchivePlugin`

### 3. Попробуйте команду с полным именем
```
/setarchivespawn
```

### 4. Проверьте алиасы
```
/setaspawn
/sas
```

### 5. Проверьте логи сервера
Посмотрите в консоль сервера на ошибки при выполнении команды

### 6. Проверьте версию плагина
В логах при запуске должно быть:
```
[INFO]: [ArchivePlugin] ArchivePlugin has been enabled!
```

## Тестирование:

### 1. Проверьте базовую команду
```
/archiveworlds
```
Если эта команда работает, значит плагин загружен правильно

### 2. Проверьте права
```
/listarchivespawns
```
Если выдает ошибку прав доступа - проблема в правах

### 3. Проверьте конфиг
После выполнения `/setarchivespawn` проверьте файл:
`plugins/ArchivePlugin/config.yml`

Должна появиться секция:
```yaml
custom-spawns:
  world_name:
    x: 123.0
    y: 64.0
    z: 456.0
    yaw: 0.0
    pitch: 0.0
```

## Если ничего не помогает:

### Вариант 1: Ручная настройка
1. Откройте `plugins/ArchivePlugin/config.yml`
2. Добавьте секцию вручную:
```yaml
custom-spawns:
  your_world_name:
    x: 100
    y: 70
    z: -50
    yaw: 0
    pitch: 0
```
3. Перезапустите сервер

### Вариант 2: Проверка в игре
1. Встаньте где нужно
2. Нажмите F3
3. Запишите координаты XYZ
4. Добавьте их в config.yml вручную

## Команды для отладки:

```bash
# Проверить плагины
/plugins

# Проверить права
/lp user your_name permission check archiveplugin.admin

# Перезагрузить конфиг (если поддерживается)
/reload

# Проверить команды плагина
/help setarchivespawn
```

## Альтернативное решение:

Если команды совсем не работают, можете настроить spawn точки вручную в config.yml:

```yaml
custom-spawns:
  world1:
    x: 0
    y: 70
    z: 0
  world2:
    x: 100
    y: 64
    z: -100
  world3:
    x: -50
    y: 80
    z: 200
```

После этого перезапустите сервер.
