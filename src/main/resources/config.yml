# ArchivePlugin Configuration

# Settings for the world selector
world-selector:
  # Give compass to players on join
  give-compass-on-join: true
  
  # Compass item settings
  compass:
    name: "Archive World Selector"
    lore:
      - "§7Right-click to open world selector"
      - "§8Choose from available archive worlds"
    glowing: true

# GUI Settings
gui:
  title: "Archive Worlds"
  size: 27 # Single chest size
  items-per-page: 21

# ArchivePlugin Configuration
# Конфигурация плагина ArchivePlugin

# Настройки миров
worlds:
  # Папка с архивными мирами (относительно корня сервера)
  directory: "archive-worlds"

  # Автоматически загружать миры при запуске сервера
  auto-load: true

  # Копировать миры в папку сервера перед загрузкой
  copy-to-server: true

# Кастомные точки спавна для миров
# Формат: имя_мира: {x: координата_x, y: координата_y, z: координата_z, yaw: поворот, pitch: наклон}
custom-spawns:
  # Примеры (удалите или замените на свои миры):
  #
  # example_world:
  #   x: 100.5
  #   y: 64.0
  #   z: -50.5
  #   yaw: 180.0
  #   pitch: 0.0
  #
  # survival_world:
  #   x: 0.0
  #   y: 70.0
  #   z: 0.0
  #   yaw: 0.0
  #   pitch: 0.0

# Messages
messages:
  no-permission: "§cYou don't have permission to use this!"
  teleported: "§aTeleported to world: %world%"
  teleport-failed: "§cFailed to teleport to world: %world%"
  compass-received: "§aYou received the Archive World Selector!"
  players-only: "§cThis command can only be used by players!"
