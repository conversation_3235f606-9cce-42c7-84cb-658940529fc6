name: ArchivePlugin
version: 2.0.0
main: com.archiveplugin.ArchivePluginSimple
api-version: 1.21
author: ArchivePlugin
description: Simple world selector plugin with spawn point management

commands:
  archiveworlds:
    description: Open world selector GUI or manage spawn points
    usage: |
      /archiveworlds - Open world selector
      /archiveworlds setspawn - Set spawn point for current world
      /archiveworlds listspawns - List all custom spawn points
    aliases: [aw, worlds]
  setarchivespawn:
    description: Set custom spawn point for current world
    usage: /setarchivespawn
    aliases: [setaspawn, sas]
  listarchivespawns:
    description: List all custom spawn points
    usage: /listarchivespawns
    aliases: [listaspawns, las]

permissions:
  archiveplugin.use:
    description: Allows using the world selector
    default: true
  archiveplugin.admin:
    description: Admin permissions for spawn point management
    default: op
