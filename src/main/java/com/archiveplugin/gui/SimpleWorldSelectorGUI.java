package com.archiveplugin.gui;

import com.archiveplugin.ArchivePlugin;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class SimpleWorldSelectorGUI implements Listener {
    
    private static final int GUI_SIZE = 54;
    private static final int WORLDS_PER_PAGE = 28;
    
    private final ArchivePlugin plugin;
    private final Map<Player, Integer> playerPages;
    private final Map<Player, String> playerCurrentWorld;
    
    public SimpleWorldSelectorGUI(ArchivePlugin plugin) {
        this.plugin = plugin;
        this.playerPages = new HashMap<>();
        this.playerCurrentWorld = new HashMap<>();
        Bukkit.getPluginManager().registerEvents(this, plugin);
    }
    
    public void openWorldSelector(Player player) {
        openWorldSelector(player, 0);
    }
    
    public void openWorldSelector(Player player, int page) {
        List<String> worlds = plugin.getWorldManager().getAvailableWorlds();
        
        Inventory gui = Bukkit.createInventory(null, GUI_SIZE, 
            ChatColor.DARK_PURPLE + "" + ChatColor.BOLD + "Archive Worlds - Page " + (page + 1));
        
        // Add world items
        int startIndex = page * WORLDS_PER_PAGE;
        int endIndex = Math.min(startIndex + WORLDS_PER_PAGE, worlds.size());
        
        for (int i = startIndex; i < endIndex; i++) {
            String worldName = worlds.get(i);
            ItemStack worldItem = createWorldItem(worldName);
            gui.setItem(i - startIndex, worldItem);
        }
        
        // Add navigation items
        addNavigationItems(gui, page, worlds.size(), player);
        
        playerPages.put(player, page);
        playerCurrentWorld.put(player, player.getWorld().getName());
        player.openInventory(gui);
    }
    
    private ItemStack createWorldItem(String worldName) {
        ItemStack item = new ItemStack(Material.GRASS_BLOCK);
        ItemMeta meta = item.getItemMeta();
        meta.setDisplayName(ChatColor.GREEN + "" + ChatColor.BOLD + worldName);
        meta.setLore(Arrays.asList(
            ChatColor.GRAY + "Click to teleport to this world",
            ChatColor.YELLOW + "World: " + worldName
        ));
        item.setItemMeta(meta);
        return item;
    }
    
    private void addNavigationItems(Inventory gui, int currentPage, int totalWorlds, Player player) {
        int totalPages = (int) Math.ceil((double) totalWorlds / WORLDS_PER_PAGE);
        
        // Previous page button
        if (currentPage > 0) {
            ItemStack prevButton = new ItemStack(Material.ARROW);
            ItemMeta meta = prevButton.getItemMeta();
            meta.setDisplayName(ChatColor.YELLOW + "" + ChatColor.BOLD + "Previous Page");
            meta.setLore(Arrays.asList(ChatColor.GRAY + "Go to page " + currentPage));
            prevButton.setItemMeta(meta);
            gui.setItem(45, prevButton);
        }
        
        // Set Spawn Point button (only for admins)
        ItemStack setSpawnButton = new ItemStack(Material.COMPASS);
        ItemMeta spawnMeta = setSpawnButton.getItemMeta();
        spawnMeta.setDisplayName(ChatColor.GOLD + "" + ChatColor.BOLD + "Set Spawn Point");
        spawnMeta.setLore(Arrays.asList(
            ChatColor.GRAY + "Click to set spawn point for current world",
            ChatColor.DARK_GRAY + "Requires admin permissions",
            ChatColor.YELLOW + "Current world: " + playerCurrentWorld.get(player)
        ));
        setSpawnButton.setItemMeta(spawnMeta);
        gui.setItem(46, setSpawnButton);
        
        // Close button
        ItemStack closeButton = new ItemStack(Material.BARRIER);
        ItemMeta closeMeta = closeButton.getItemMeta();
        closeMeta.setDisplayName(ChatColor.RED + "" + ChatColor.BOLD + "Close");
        closeMeta.setLore(Arrays.asList(ChatColor.GRAY + "Click to close this menu"));
        closeButton.setItemMeta(closeMeta);
        gui.setItem(49, closeButton);
        
        // Next page button
        if (currentPage < totalPages - 1) {
            ItemStack nextButton = new ItemStack(Material.ARROW);
            ItemMeta meta = nextButton.getItemMeta();
            meta.setDisplayName(ChatColor.YELLOW + "" + ChatColor.BOLD + "Next Page");
            meta.setLore(Arrays.asList(ChatColor.GRAY + "Go to page " + (currentPage + 2)));
            nextButton.setItemMeta(meta);
            gui.setItem(53, nextButton);
        }
        
        // Page info
        ItemStack pageInfo = new ItemStack(Material.BOOK);
        ItemMeta pageInfoMeta = pageInfo.getItemMeta();
        pageInfoMeta.setDisplayName(ChatColor.AQUA + "" + ChatColor.BOLD + "Page " + (currentPage + 1) + "/" + Math.max(1, totalPages));
        pageInfoMeta.setLore(Arrays.asList(
            ChatColor.GRAY + "Total worlds: " + totalWorlds,
            ChatColor.GRAY + "Worlds per page: " + WORLDS_PER_PAGE
        ));
        pageInfo.setItemMeta(pageInfoMeta);
        gui.setItem(50, pageInfo);
    }
    
    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) {
        if (!(event.getWhoClicked() instanceof Player player)) return;
        
        String title = event.getView().getTitle();
        if (!title.contains("Archive Worlds")) return;
        
        event.setCancelled(true);
        
        ItemStack clickedItem = event.getCurrentItem();
        if (clickedItem == null || clickedItem.getType() == Material.AIR) return;
        
        int slot = event.getSlot();
        
        // Handle navigation buttons
        if (slot == 45 && clickedItem.getType() == Material.ARROW) {
            // Previous page
            Integer currentPage = playerPages.get(player);
            if (currentPage != null && currentPage > 0) {
                openWorldSelector(player, currentPage - 1);
            }
            return;
        }
        
        if (slot == 53 && clickedItem.getType() == Material.ARROW) {
            // Next page
            Integer currentPage = playerPages.get(player);
            if (currentPage != null) {
                openWorldSelector(player, currentPage + 1);
            }
            return;
        }
        
        if (slot == 46 && clickedItem.getType() == Material.COMPASS) {
            // Set Spawn Point
            if (!player.hasPermission("archiveplugin.admin") && !player.isOp()) {
                player.sendMessage(ChatColor.RED + "❌ You don't have permission to set spawn points!");
                return;
            }
            
            String currentWorld = playerCurrentWorld.get(player);
            if (currentWorld == null) {
                currentWorld = player.getWorld().getName();
            }
            
            // Set spawn point using the same logic as the command
            Location loc = player.getLocation();
            
            // Save to config
            FileConfiguration config = plugin.getConfig();
            String path = "custom-spawns." + currentWorld;
            config.set(path + ".x", loc.getX());
            config.set(path + ".y", loc.getY());
            config.set(path + ".z", loc.getZ());
            config.set(path + ".yaw", loc.getYaw());
            config.set(path + ".pitch", loc.getPitch());
            
            plugin.saveConfig();
            
            player.closeInventory();
            playerPages.remove(player);
            playerCurrentWorld.remove(player);
            
            player.sendMessage(ChatColor.GREEN + "✅ Spawn point set for world '" + currentWorld + "' at:");
            player.sendMessage(ChatColor.YELLOW + "📍 X: " + (int)loc.getX() + ", Y: " + (int)loc.getY() + ", Z: " + (int)loc.getZ());
            
            plugin.getLogger().info("Player " + player.getName() + " set spawn for world " + currentWorld + 
                                   " at " + (int)loc.getX() + ", " + (int)loc.getY() + ", " + (int)loc.getZ());
            return;
        }
        
        if (slot == 49 && clickedItem.getType() == Material.BARRIER) {
            // Close
            player.closeInventory();
            playerPages.remove(player);
            playerCurrentWorld.remove(player);
            return;
        }
        
        // Handle world selection
        if (slot < WORLDS_PER_PAGE && clickedItem.getType() == Material.GRASS_BLOCK) {
            ItemMeta meta = clickedItem.getItemMeta();
            if (meta != null && meta.getDisplayName() != null) {
                String worldName = ChatColor.stripColor(meta.getDisplayName());
                
                if (plugin.getWorldManager().teleportPlayerToWorld(player, worldName)) {
                    player.sendMessage(ChatColor.GREEN + "Teleported to world: " + worldName);
                } else {
                    player.sendMessage(ChatColor.RED + "Failed to teleport to world: " + worldName);
                }
                
                player.closeInventory();
                playerPages.remove(player);
                playerCurrentWorld.remove(player);
            }
        }
    }
}
