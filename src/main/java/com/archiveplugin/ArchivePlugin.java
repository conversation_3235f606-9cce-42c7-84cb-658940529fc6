package com.archiveplugin;

import com.archiveplugin.listeners.PlayerListener;
import com.archiveplugin.managers.SimpleWorldManager;
import com.archiveplugin.managers.SpawnManager;
import com.archiveplugin.commands.WorldCommand;
import com.archiveplugin.commands.SetSpawnCommand;
import com.archiveplugin.commands.ListSpawnsCommand;
import com.archiveplugin.gui.WorldSelectorGUI;
import org.bukkit.plugin.java.JavaPlugin;

import java.io.File;
import java.util.logging.Logger;

public class ArchivePlugin extends JavaPlugin {
    
    private static ArchivePlugin instance;
    private SimpleWorldManager worldManager;
    private SpawnManager spawnManager;
    private WorldSelectorGUI worldSelectorGUI;
    private Logger logger;
    
    @Override
    public void onEnable() {
        instance = this;
        logger = getLogger();
        
        // Create worlds directory
        createWorldsDirectory();
        
        // Initialize managers
        worldManager = new SimpleWorldManager(this);
        spawnManager = new SpawnManager(this);
        worldSelectorGUI = new WorldSelectorGUI(this);

        // Register listeners
        getServer().getPluginManager().registerEvents(new PlayerListener(this), this);

        // Register commands
        getCommand("archiveworlds").setExecutor(new WorldCommand(this));
        getCommand("setarchivespawn").setExecutor(new SetSpawnCommand(this));
        getCommand("listarchivespawns").setExecutor(new ListSpawnsCommand(this));
        
        logger.info("ArchivePlugin has been enabled!");
    }
    
    @Override
    public void onDisable() {
        logger.info("ArchivePlugin has been disabled!");
    }
    
    private void createWorldsDirectory() {
        File worldsDir = new File(getDataFolder().getParentFile(), "archive-worlds");
        if (!worldsDir.exists()) {
            if (worldsDir.mkdirs()) {
                logger.info("Created archive-worlds directory: " + worldsDir.getAbsolutePath());
            } else {
                logger.severe("Failed to create archive-worlds directory!");
            }
        }
    }
    
    public static ArchivePlugin getInstance() {
        return instance;
    }
    
    public SimpleWorldManager getWorldManager() {
        return worldManager;
    }

    public SpawnManager getSpawnManager() {
        return spawnManager;
    }

    public WorldSelectorGUI getWorldSelectorGUI() {
        return worldSelectorGUI;
    }
}
