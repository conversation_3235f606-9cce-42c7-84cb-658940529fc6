package com.archiveplugin;

import com.archiveplugin.listeners.PlayerListener;
import com.archiveplugin.managers.WorldManager;
import com.archiveplugin.commands.WorldCommand;
import com.archiveplugin.gui.WorldSelectorGUI;
import org.bukkit.plugin.java.JavaPlugin;

import java.io.File;
import java.util.logging.Logger;

public class ArchivePlugin extends JavaPlugin {
    
    private static ArchivePlugin instance;
    private WorldManager worldManager;
    private WorldSelectorGUI worldSelectorGUI;
    private Logger logger;
    
    @Override
    public void onEnable() {
        instance = this;
        logger = getLogger();
        
        // Create worlds directory
        createWorldsDirectory();
        
        // Initialize managers
        worldManager = new WorldManager(this);
        worldSelectorGUI = new WorldSelectorGUI(this);

        // Register listeners
        getServer().getPluginManager().registerEvents(new PlayerListener(this), this);

        // Register commands
        getCommand("archiveworlds").setExecutor(new WorldCommand(this));
        
        logger.info("ArchivePlugin has been enabled!");
    }
    
    @Override
    public void onDisable() {
        logger.info("ArchivePlugin has been disabled!");
    }
    
    private void createWorldsDirectory() {
        File worldsDir = new File(getDataFolder().getParentFile(), "archive-worlds");
        if (!worldsDir.exists()) {
            if (worldsDir.mkdirs()) {
                logger.info("Created archive-worlds directory: " + worldsDir.getAbsolutePath());
            } else {
                logger.severe("Failed to create archive-worlds directory!");
            }
        }
    }
    
    public static ArchivePlugin getInstance() {
        return instance;
    }
    
    public WorldManager getWorldManager() {
        return worldManager;
    }

    public WorldSelectorGUI getWorldSelectorGUI() {
        return worldSelectorGUI;
    }
}
