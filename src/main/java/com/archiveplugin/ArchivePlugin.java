package com.archiveplugin;

import com.archiveplugin.listeners.PlayerListener;
import com.archiveplugin.managers.ConfigWorldManager;
import com.archiveplugin.managers.SpawnManager;
import com.archiveplugin.commands.WorldCommand;
import com.archiveplugin.commands.SimpleSetSpawnCommand;
import com.archiveplugin.commands.SimpleListSpawnsCommand;
import com.archiveplugin.gui.WorldSelectorGUI;
import org.bukkit.plugin.java.JavaPlugin;

import java.io.File;
import java.util.logging.Logger;

public class ArchivePlugin extends JavaPlugin {
    
    private static ArchivePlugin instance;
    private ConfigWorldManager worldManager;
    private SpawnManager spawnManager;
    private WorldSelectorGUI worldSelectorGUI;
    private Logger logger;
    
    @Override
    public void onEnable() {
        instance = this;
        logger = getLogger();
        
        // Create worlds directory and config
        createWorldsDirectory();
        saveDefaultConfig();
        
        // Initialize managers
        worldManager = new ConfigWorldManager(this);
        spawnManager = new SpawnManager(this);
        worldSelectorGUI = new WorldSelectorGUI(this);

        // Register listeners
        getServer().getPluginManager().registerEvents(new PlayerListener(this), this);

        // Register commands
        getCommand("archiveworlds").setExecutor(new WorldCommand(this));
        getCommand("setarchivespawn").setExecutor(new SimpleSetSpawnCommand(this));
        getCommand("listarchivespawns").setExecutor(new SimpleListSpawnsCommand(this));
        
        logger.info("ArchivePlugin has been enabled!");
    }
    
    @Override
    public void onDisable() {
        logger.info("ArchivePlugin has been disabled!");
    }
    
    private void createWorldsDirectory() {
        File worldsDir = new File(getDataFolder().getParentFile(), "archive-worlds");
        if (!worldsDir.exists()) {
            if (worldsDir.mkdirs()) {
                logger.info("Created archive-worlds directory: " + worldsDir.getAbsolutePath());
            } else {
                logger.severe("Failed to create archive-worlds directory!");
            }
        }
    }
    
    public static ArchivePlugin getInstance() {
        return instance;
    }
    
    public ConfigWorldManager getWorldManager() {
        return worldManager;
    }

    public SpawnManager getSpawnManager() {
        return spawnManager;
    }

    public WorldSelectorGUI getWorldSelectorGUI() {
        return worldSelectorGUI;
    }
}
