package com.archiveplugin.listeners;

import com.archiveplugin.ArchivePlugin;
import com.archiveplugin.gui.WorldSelectorGUI;
import com.archiveplugin.utils.ItemBuilder;
import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.format.NamedTextColor;
import net.kyori.adventure.text.format.TextDecoration;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.block.Action;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.event.player.PlayerJoinEvent;
import org.bukkit.inventory.ItemStack;

public class PlayerListener implements Listener {
    
    private final ArchivePlugin plugin;

    public PlayerListener(ArchivePlugin plugin) {
        this.plugin = plugin;
    }
    
    @EventHandler
    public void onPlayerJoin(PlayerJoinEvent event) {
        Player player = event.getPlayer();
        
        // Give compass to player
        giveCompass(player);
    }
    
    @EventHandler
    public void onPlayerInteract(PlayerInteractEvent event) {
        Player player = event.getPlayer();
        ItemStack item = event.getItem();
        
        if (item == null || item.getType() != Material.COMPASS) {
            return;
        }
        
        // Check if it's our special compass
        if (item.getItemMeta() != null && item.getItemMeta().displayName() != null) {
            Component displayName = item.getItemMeta().displayName();
            if (displayName instanceof net.kyori.adventure.text.TextComponent textComponent) {
                if ("Archive World Selector".equals(textComponent.content())) {
                    if (event.getAction() == Action.RIGHT_CLICK_AIR || event.getAction() == Action.RIGHT_CLICK_BLOCK) {
                        event.setCancelled(true);
                        
                        if (player.hasPermission("archiveplugin.use")) {
                            plugin.getWorldSelectorGUI().openWorldSelector(player);
                        } else {
                            player.sendMessage(Component.text("You don't have permission to use this!")
                                .color(NamedTextColor.RED));
                        }
                    }
                }
            }
        }
    }
    
    private void giveCompass(Player player) {
        // Check if player already has the compass
        for (ItemStack item : player.getInventory().getContents()) {
            if (item != null && item.getType() == Material.COMPASS) {
                if (item.getItemMeta() != null && item.getItemMeta().displayName() != null) {
                    Component displayName = item.getItemMeta().displayName();
                    if (displayName instanceof net.kyori.adventure.text.TextComponent textComponent) {
                        if ("Archive World Selector".equals(textComponent.content())) {
                            return; // Player already has the compass
                        }
                    }
                }
            }
        }
        
        // Create and give the compass
        ItemStack compass = new ItemBuilder(Material.COMPASS)
            .setDisplayName(Component.text("Archive World Selector")
                .color(NamedTextColor.GOLD)
                .decoration(TextDecoration.BOLD, true))
            .addLoreLine("§7Right-click to open world selector")
            .addLoreLine("§8Choose from available archive worlds")
            .setGlowing()
            .build();
        
        player.getInventory().addItem(compass);
        player.sendMessage(Component.text("You received the Archive World Selector!")
            .color(NamedTextColor.GREEN));
    }
}
