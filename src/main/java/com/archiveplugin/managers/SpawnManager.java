package com.archiveplugin.managers;

import com.archiveplugin.ArchivePlugin;
import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.World;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;

import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.logging.Logger;

public class SpawnManager {
    
    private final ArchivePlugin plugin;
    private final Logger logger;
    private final File spawnFile;
    private FileConfiguration spawnConfig;
    private final Map<String, Location> customSpawns;
    
    public SpawnManager(ArchivePlugin plugin) {
        this.plugin = plugin;
        this.logger = plugin.getLogger();
        this.customSpawns = new HashMap<>();
        
        // Create spawns.yml file
        this.spawnFile = new File(plugin.getDataFolder(), "spawns.yml");
        loadSpawnConfig();
        loadCustomSpawns();
    }
    
    private void loadSpawnConfig() {
        if (!spawnFile.exists()) {
            try {
                spawnFile.getParentFile().mkdirs();
                spawnFile.createNewFile();
                logger.info("Created spawns.yml file");
            } catch (IOException e) {
                logger.severe("Could not create spawns.yml file: " + e.getMessage());
            }
        }
        
        spawnConfig = YamlConfiguration.loadConfiguration(spawnFile);
    }
    
    private void loadCustomSpawns() {
        customSpawns.clear();
        
        if (!spawnConfig.contains("spawns")) {
            return;
        }
        
        for (String worldName : spawnConfig.getConfigurationSection("spawns").getKeys(false)) {
            try {
                String path = "spawns." + worldName;
                double x = spawnConfig.getDouble(path + ".x");
                double y = spawnConfig.getDouble(path + ".y");
                double z = spawnConfig.getDouble(path + ".z");
                float yaw = (float) spawnConfig.getDouble(path + ".yaw", 0);
                float pitch = (float) spawnConfig.getDouble(path + ".pitch", 0);
                
                World world = Bukkit.getWorld(worldName);
                if (world != null) {
                    Location location = new Location(world, x, y, z, yaw, pitch);
                    customSpawns.put(worldName, location);
                    logger.info("Loaded custom spawn for world: " + worldName);
                }
            } catch (Exception e) {
                logger.warning("Failed to load spawn for world " + worldName + ": " + e.getMessage());
            }
        }
    }
    
    public void setCustomSpawn(String worldName, Location location) {
        customSpawns.put(worldName, location.clone());
        
        // Save to config
        String path = "spawns." + worldName;
        spawnConfig.set(path + ".x", location.getX());
        spawnConfig.set(path + ".y", location.getY());
        spawnConfig.set(path + ".z", location.getZ());
        spawnConfig.set(path + ".yaw", location.getYaw());
        spawnConfig.set(path + ".pitch", location.getPitch());
        
        saveSpawnConfig();
        logger.info("Set custom spawn for world " + worldName + " at " + 
                   (int)location.getX() + ", " + (int)location.getY() + ", " + (int)location.getZ());
    }
    
    public Location getCustomSpawn(String worldName) {
        return customSpawns.get(worldName);
    }
    
    public Map<String, Location> getCustomSpawns() {
        return new HashMap<>(customSpawns);
    }
    
    public boolean hasCustomSpawn(String worldName) {
        return customSpawns.containsKey(worldName);
    }
    
    public void removeCustomSpawn(String worldName) {
        customSpawns.remove(worldName);
        spawnConfig.set("spawns." + worldName, null);
        saveSpawnConfig();
        logger.info("Removed custom spawn for world: " + worldName);
    }
    
    private void saveSpawnConfig() {
        try {
            spawnConfig.save(spawnFile);
        } catch (IOException e) {
            logger.severe("Could not save spawns.yml: " + e.getMessage());
        }
    }
    
    public void reloadSpawns() {
        loadSpawnConfig();
        loadCustomSpawns();
        logger.info("Reloaded custom spawns");
    }
}
