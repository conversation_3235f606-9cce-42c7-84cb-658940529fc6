package com.archiveplugin.managers;

import com.archiveplugin.ArchivePlugin;
import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.World;
import org.bukkit.WorldCreator;
import org.bukkit.block.Block;
import org.bukkit.entity.Player;
import org.bukkit.Chunk;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Logger;

public class WorldManager {
    
    private final ArchivePlugin plugin;
    private final Logger logger;
    private final File worldsDirectory;
    
    public WorldManager(ArchivePlugin plugin) {
        this.plugin = plugin;
        this.logger = plugin.getLogger();
        this.worldsDirectory = new File(plugin.getDataFolder().getParentFile(), "archive-worlds");
        
        loadAvailableWorlds();
    }
    
    public List<String> getAvailableWorlds() {
        List<String> worlds = new ArrayList<>();
        
        if (!worldsDirectory.exists()) {
            return worlds;
        }
        
        File[] worldFolders = worldsDirectory.listFiles(File::isDirectory);
        if (worldFolders != null) {
            for (File worldFolder : worldFolders) {
                // Check if it's a valid Minecraft world (has level.dat)
                File levelDat = new File(worldFolder, "level.dat");
                if (levelDat.exists()) {
                    worlds.add(worldFolder.getName());
                }
            }
        }
        
        return worlds;
    }
    
    public void loadAvailableWorlds() {
        List<String> availableWorlds = getAvailableWorlds();
        
        for (String worldName : availableWorlds) {
            if (Bukkit.getWorld(worldName) == null) {
                try {
                    // Copy world to server directory if not exists
                    File serverWorldDir = new File(Bukkit.getWorldContainer(), worldName);
                    File archiveWorldDir = new File(worldsDirectory, worldName);
                    
                    if (!serverWorldDir.exists() && archiveWorldDir.exists()) {
                        copyWorld(archiveWorldDir, serverWorldDir);
                    }
                    
                    // Load the world
                    WorldCreator creator = new WorldCreator(worldName);
                    World world = creator.createWorld();
                    
                    if (world != null) {
                        logger.info("Loaded world: " + worldName);
                    }
                } catch (Exception e) {
                    logger.warning("Failed to load world: " + worldName + " - " + e.getMessage());
                }
            }
        }
    }
    
    public boolean teleportPlayerToWorld(Player player, String worldName) {
        World world = Bukkit.getWorld(worldName);

        if (world == null) {
            // Try to load the world
            try {
                File serverWorldDir = new File(Bukkit.getWorldContainer(), worldName);
                File archiveWorldDir = new File(worldsDirectory, worldName);

                if (!serverWorldDir.exists() && archiveWorldDir.exists()) {
                    copyWorld(archiveWorldDir, serverWorldDir);
                }

                WorldCreator creator = new WorldCreator(worldName);
                world = creator.createWorld();
            } catch (Exception e) {
                logger.warning("Failed to load world for teleportation: " + worldName + " - " + e.getMessage());
                return false;
            }
        }

        if (world != null) {
            Location teleportLocation = findBestTeleportLocation(world, player);
            player.teleport(teleportLocation);
            return true;
        }

        return false;
    }

    private Location findBestTeleportLocation(World world, Player player) {
        logger.info("Searching for best teleport location in world: " + world.getName());

        // Strategy 1: Try to find player's last position from playerdata
        Location playerDataLocation = findPlayerLastPosition(world, player);
        if (playerDataLocation != null) {
            logger.info("Found player's last position in world");
            return playerDataLocation;
        }

        // Strategy 2: Search for structures/builds in the world
        Location structureLocation = findNearestStructure(world);
        if (structureLocation != null) {
            logger.info("Found structure at: " + structureLocation.getBlockX() + ", " + structureLocation.getBlockY() + ", " + structureLocation.getBlockZ());
            return structureLocation;
        }

        // Strategy 3: Find highest non-air block near spawn
        Location highestLocation = findHighestSafeLocation(world, world.getSpawnLocation());
        if (highestLocation != null) {
            logger.info("Using highest safe location near spawn");
            return highestLocation;
        }

        // Fallback: Use world spawn
        logger.info("Using world spawn as fallback");
        return world.getSpawnLocation();
    }

    private void copyWorld(File source, File target) {
        try {
            if (!target.exists()) {
                target.mkdirs();
            }
            
            copyDirectory(source, target);
        } catch (Exception e) {
            logger.warning("Failed to copy world from " + source.getName() + " to " + target.getName() + ": " + e.getMessage());
        }
    }
    
    private void copyDirectory(File source, File target) throws Exception {
        if (source.isDirectory()) {
            if (!target.exists()) {
                target.mkdirs();
            }
            
            String[] files = source.list();
            if (files != null) {
                for (String file : files) {
                    File srcFile = new File(source, file);
                    File destFile = new File(target, file);
                    copyDirectory(srcFile, destFile);
                }
            }
        } else {
            java.nio.file.Files.copy(source.toPath(), target.toPath(), 
                java.nio.file.StandardCopyOption.REPLACE_EXISTING);
        }
    }

    private Location findPlayerLastPosition(World world, Player player) {
        // Try to read player's last position from playerdata
        // This is a simplified approach - in reality, you'd need to parse NBT data
        // For now, we'll skip this and rely on other methods
        return null;
    }

    private Location findNearestStructure(World world) {
        logger.info("Scanning world for structures...");

        // Search in a spiral pattern around spawn
        Location spawn = world.getSpawnLocation();
        int maxRadius = 10; // Search 10 chunks in each direction

        for (int radius = 1; radius <= maxRadius; radius++) {
            for (int x = -radius; x <= radius; x++) {
                for (int z = -radius; z <= radius; z++) {
                    // Only check the edge of the current radius
                    if (Math.abs(x) != radius && Math.abs(z) != radius) {
                        continue;
                    }

                    int chunkX = (spawn.getBlockX() >> 4) + x;
                    int chunkZ = (spawn.getBlockZ() >> 4) + z;

                    Location structureLocation = scanChunkForStructures(world, chunkX, chunkZ);
                    if (structureLocation != null) {
                        return structureLocation;
                    }
                }
            }
        }

        return null;
    }

    private Location scanChunkForStructures(World world, int chunkX, int chunkZ) {
        try {
            // Load the chunk if it's not loaded
            Chunk chunk = world.getChunkAt(chunkX, chunkZ);
            if (!chunk.isLoaded()) {
                chunk.load();
            }

            // Scan the chunk for interesting blocks (signs of player activity)
            for (int x = 0; x < 16; x++) {
                for (int z = 0; z < 16; z++) {
                    int worldX = chunkX * 16 + x;
                    int worldZ = chunkZ * 16 + z;

                    // Check from top to bottom for interesting blocks
                    for (int y = world.getMaxHeight() - 1; y >= world.getMinHeight(); y--) {
                        Block block = world.getBlockAt(worldX, y, worldZ);

                        if (isInterestingBlock(block)) {
                            // Found an interesting block, find a safe location nearby
                            Location safeLocation = findSafeLocationNear(world, worldX, y, worldZ);
                            if (safeLocation != null) {
                                return safeLocation;
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            logger.warning("Error scanning chunk " + chunkX + "," + chunkZ + ": " + e.getMessage());
        }

        return null;
    }

    private boolean isInterestingBlock(Block block) {
        Material type = block.getType();

        // Check for blocks that indicate player activity
        return type == Material.CHEST ||
               type == Material.CRAFTING_TABLE ||
               type == Material.FURNACE ||
               type == Material.BLAST_FURNACE ||
               type == Material.SMOKER ||
               type == Material.ENCHANTING_TABLE ||
               type == Material.ANVIL ||
               type == Material.CHIPPED_ANVIL ||
               type == Material.DAMAGED_ANVIL ||
               type == Material.BREWING_STAND ||
               type == Material.CAULDRON ||
               type == Material.LECTERN ||
               type == Material.LOOM ||
               type == Material.STONECUTTER ||
               type == Material.SMITHING_TABLE ||
               type == Material.CARTOGRAPHY_TABLE ||
               type == Material.FLETCHING_TABLE ||
               type.name().contains("BED") ||
               type.name().contains("DOOR") ||
               type.name().contains("SIGN") ||
               type.name().contains("BANNER") ||
               type == Material.TORCH ||
               type == Material.SOUL_TORCH ||
               type == Material.REDSTONE_TORCH ||
               type.name().contains("LANTERN") ||
               type == Material.GLOWSTONE ||
               type.name().contains("GLASS") ||
               type.name().contains("STAIRS") ||
               type.name().contains("SLAB") ||
               type.name().contains("FENCE") ||
               type.name().contains("WALL") && !type.name().contains("WALLPAPER");
    }

    private Location findSafeLocationNear(World world, int x, int y, int z) {
        // Try to find a safe location (2 blocks of air above solid ground) near the given coordinates
        for (int radius = 0; radius <= 5; radius++) {
            for (int dx = -radius; dx <= radius; dx++) {
                for (int dz = -radius; dz <= radius; dz++) {
                    for (int dy = -2; dy <= 5; dy++) {
                        int checkX = x + dx;
                        int checkY = y + dy;
                        int checkZ = z + dz;

                        if (isSafeLocation(world, checkX, checkY, checkZ)) {
                            return new Location(world, checkX + 0.5, checkY, checkZ + 0.5);
                        }
                    }
                }
            }
        }
        return null;
    }

    private Location findHighestSafeLocation(World world, Location center) {
        // Search around the center location for the highest safe spot
        for (int radius = 0; radius <= 20; radius++) {
            for (int x = -radius; x <= radius; x++) {
                for (int z = -radius; z <= radius; z++) {
                    int worldX = center.getBlockX() + x;
                    int worldZ = center.getBlockZ() + z;

                    // Find the highest solid block
                    for (int y = world.getMaxHeight() - 1; y >= world.getMinHeight(); y--) {
                        if (isSafeLocation(world, worldX, y, worldZ)) {
                            return new Location(world, worldX + 0.5, y, worldZ + 0.5);
                        }
                    }
                }
            }
        }
        return null;
    }

    private boolean isSafeLocation(World world, int x, int y, int z) {
        try {
            // Check if the location is safe for teleportation
            Block ground = world.getBlockAt(x, y - 1, z);
            Block feet = world.getBlockAt(x, y, z);
            Block head = world.getBlockAt(x, y + 1, z);

            // Ground should be solid, feet and head should be air or passable
            return ground.getType().isSolid() &&
                   !ground.getType().name().contains("LAVA") &&
                   !ground.getType().name().contains("MAGMA") &&
                   (feet.getType().isAir() || isPassable(feet.getType())) &&
                   (head.getType().isAir() || isPassable(head.getType()));
        } catch (Exception e) {
            return false;
        }
    }

    private boolean isPassable(Material material) {
        // Check if a material is passable (player can walk through it)
        return material.isAir() ||
               material == Material.WATER ||
               material == Material.TALL_GRASS ||
               material == Materialе.GRASS ||
               material == Material.FERN ||
               material == Material.LARGE_FERN ||
               material == Material.DEAD_BUSH ||
               material.name().contains("SAPLING") ||
               material.name().contains("FLOWER") ||
               material == Material.SNOW;
    }
}
