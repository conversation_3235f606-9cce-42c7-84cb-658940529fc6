package com.archiveplugin.managers;

import com.archiveplugin.ArchivePlugin;
import org.bukkit.Bukkit;
import org.bukkit.World;
import org.bukkit.WorldCreator;
import org.bukkit.entity.Player;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Logger;

public class WorldManager {
    
    private final ArchivePlugin plugin;
    private final Logger logger;
    private final File worldsDirectory;
    
    public WorldManager(ArchivePlugin plugin) {
        this.plugin = plugin;
        this.logger = plugin.getLogger();
        this.worldsDirectory = new File(plugin.getDataFolder().getParentFile(), "archive-worlds");
        
        loadAvailableWorlds();
    }
    
    public List<String> getAvailableWorlds() {
        List<String> worlds = new ArrayList<>();
        
        if (!worldsDirectory.exists()) {
            return worlds;
        }
        
        File[] worldFolders = worldsDirectory.listFiles(File::isDirectory);
        if (worldFolders != null) {
            for (File worldFolder : worldFolders) {
                // Check if it's a valid Minecraft world (has level.dat)
                File levelDat = new File(worldFolder, "level.dat");
                if (levelDat.exists()) {
                    worlds.add(worldFolder.getName());
                }
            }
        }
        
        return worlds;
    }
    
    public void loadAvailableWorlds() {
        List<String> availableWorlds = getAvailableWorlds();
        
        for (String worldName : availableWorlds) {
            if (Bukkit.getWorld(worldName) == null) {
                try {
                    // Copy world to server directory if not exists
                    File serverWorldDir = new File(Bukkit.getWorldContainer(), worldName);
                    File archiveWorldDir = new File(worldsDirectory, worldName);
                    
                    if (!serverWorldDir.exists() && archiveWorldDir.exists()) {
                        copyWorld(archiveWorldDir, serverWorldDir);
                    }
                    
                    // Load the world
                    WorldCreator creator = new WorldCreator(worldName);
                    World world = creator.createWorld();
                    
                    if (world != null) {
                        logger.info("Loaded world: " + worldName);
                    }
                } catch (Exception e) {
                    logger.warning("Failed to load world: " + worldName + " - " + e.getMessage());
                }
            }
        }
    }
    
    public boolean teleportPlayerToWorld(Player player, String worldName) {
        World world = Bukkit.getWorld(worldName);
        
        if (world == null) {
            // Try to load the world
            try {
                File serverWorldDir = new File(Bukkit.getWorldContainer(), worldName);
                File archiveWorldDir = new File(worldsDirectory, worldName);
                
                if (!serverWorldDir.exists() && archiveWorldDir.exists()) {
                    copyWorld(archiveWorldDir, serverWorldDir);
                }
                
                WorldCreator creator = new WorldCreator(worldName);
                world = creator.createWorld();
            } catch (Exception e) {
                logger.warning("Failed to load world for teleportation: " + worldName + " - " + e.getMessage());
                return false;
            }
        }
        
        if (world != null) {
            player.teleport(world.getSpawnLocation());
            return true;
        }
        
        return false;
    }
    
    private void copyWorld(File source, File target) {
        try {
            if (!target.exists()) {
                target.mkdirs();
            }
            
            copyDirectory(source, target);
        } catch (Exception e) {
            logger.warning("Failed to copy world from " + source.getName() + " to " + target.getName() + ": " + e.getMessage());
        }
    }
    
    private void copyDirectory(File source, File target) throws Exception {
        if (source.isDirectory()) {
            if (!target.exists()) {
                target.mkdirs();
            }
            
            String[] files = source.list();
            if (files != null) {
                for (String file : files) {
                    File srcFile = new File(source, file);
                    File destFile = new File(target, file);
                    copyDirectory(srcFile, destFile);
                }
            }
        } else {
            java.nio.file.Files.copy(source.toPath(), target.toPath(), 
                java.nio.file.StandardCopyOption.REPLACE_EXISTING);
        }
    }
}
