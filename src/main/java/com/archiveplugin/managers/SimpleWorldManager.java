package com.archiveplugin.managers;

import com.archiveplugin.ArchivePlugin;
import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.World;
import org.bukkit.WorldCreator;
import org.bukkit.entity.Player;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Logger;

public class SimpleWorldManager {
    
    private final ArchivePlugin plugin;
    private final Logger logger;
    private final File worldsDirectory;
    
    public SimpleWorldManager(ArchivePlugin plugin) {
        this.plugin = plugin;
        this.logger = plugin.getLogger();
        this.worldsDirectory = new File(plugin.getDataFolder().getParentFile(), "archive-worlds");
        
        loadAvailableWorlds();
    }
    
    public List<String> getAvailableWorlds() {
        List<String> worlds = new ArrayList<>();
        
        if (!worldsDirectory.exists()) {
            return worlds;
        }
        
        File[] worldFolders = worldsDirectory.listFiles(File::isDirectory);
        if (worldFolders != null) {
            for (File worldFolder : worldFolders) {
                // Check if it's a valid Minecraft world (has level.dat)
                File levelDat = new File(worldFolder, "level.dat");
                if (levelDat.exists()) {
                    worlds.add(worldFolder.getName());
                }
            }
        }
        
        return worlds;
    }
    
    public void loadAvailableWorlds() {
        List<String> worlds = getAvailableWorlds();
        
        for (String worldName : worlds) {
            try {
                if (Bukkit.getWorld(worldName) == null) {
                    // Copy world to server directory if needed
                    File serverWorldDir = new File(Bukkit.getWorldContainer(), worldName);
                    File archiveWorldDir = new File(worldsDirectory, worldName);
                    
                    if (!serverWorldDir.exists() && archiveWorldDir.exists()) {
                        copyWorld(archiveWorldDir, serverWorldDir);
                    }
                    
                    // Load the world
                    WorldCreator creator = new WorldCreator(worldName);
                    World world = creator.createWorld();
                    
                    if (world != null) {
                        logger.info("Loaded world: " + worldName);
                    }
                }
            } catch (Exception e) {
                logger.warning("Failed to load world: " + worldName + " - " + e.getMessage());
            }
        }
    }
    
    public boolean teleportPlayerToWorld(Player player, String worldName) {
        World world = Bukkit.getWorld(worldName);
        
        if (world == null) {
            // Try to load the world
            try {
                File serverWorldDir = new File(Bukkit.getWorldContainer(), worldName);
                File archiveWorldDir = new File(worldsDirectory, worldName);
                
                if (!serverWorldDir.exists() && archiveWorldDir.exists()) {
                    copyWorld(archiveWorldDir, serverWorldDir);
                }
                
                WorldCreator creator = new WorldCreator(worldName);
                world = creator.createWorld();
            } catch (Exception e) {
                logger.warning("Failed to load world for teleportation: " + worldName + " - " + e.getMessage());
                return false;
            }
        }
        
        if (world != null) {
            Location teleportLocation;
            
            // Check if there's a custom spawn point
            if (plugin.getSpawnManager().hasCustomSpawn(worldName)) {
                teleportLocation = plugin.getSpawnManager().getCustomSpawn(worldName);
                logger.info("Using custom spawn for " + worldName);
            } else {
                teleportLocation = world.getSpawnLocation();
                logger.info("Using default spawn for " + worldName);
            }
            
            player.teleport(teleportLocation);
            return true;
        }
        
        return false;
    }
    
    private void copyWorld(File source, File target) {
        try {
            if (!target.exists()) {
                target.mkdirs();
            }
            
            copyDirectory(source.toPath(), target.toPath());
        } catch (Exception e) {
            logger.warning("Failed to copy world from " + source.getName() + " to " + target.getName() + ": " + e.getMessage());
        }
    }
    
    private void copyDirectory(Path source, Path target) throws IOException {
        Files.walk(source).forEach(sourcePath -> {
            try {
                Path targetPath = target.resolve(source.relativize(sourcePath));
                if (Files.isDirectory(sourcePath)) {
                    Files.createDirectories(targetPath);
                } else {
                    Files.copy(sourcePath, targetPath, StandardCopyOption.REPLACE_EXISTING);
                }
            } catch (IOException e) {
                logger.warning("Failed to copy file: " + sourcePath + " - " + e.getMessage());
            }
        });
    }
}
