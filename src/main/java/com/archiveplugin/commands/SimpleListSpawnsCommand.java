package com.archiveplugin.commands;

import com.archiveplugin.ArchivePlugin;
import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.format.NamedTextColor;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.entity.Player;

public class SimpleListSpawnsCommand implements CommandExecutor {
    
    private final ArchivePlugin plugin;

    public SimpleListSpawnsCommand(ArchivePlugin plugin) {
        this.plugin = plugin;
    }
    
    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!(sender instanceof Player player)) {
            sender.sendMessage(Component.text("This command can only be used by players!")
                .color(NamedTextColor.RED));
            return true;
        }
        
        if (!player.hasPermission("archiveplugin.admin") && !player.isOp()) {
            player.sendMessage(Component.text("You don't have permission to use this command!")
                .color(NamedTextColor.RED));
            return true;
        }
        
        FileConfiguration config = plugin.getConfig();
        ConfigurationSection spawnsSection = config.getConfigurationSection("custom-spawns");
        
        if (spawnsSection == null || spawnsSection.getKeys(false).isEmpty()) {
            player.sendMessage(Component.text("No custom spawn points set.")
                .color(NamedTextColor.YELLOW));
            player.sendMessage(Component.text("Use /setarchivespawn to set spawn points.")
                .color(NamedTextColor.GRAY));
            return true;
        }
        
        player.sendMessage(Component.text("Custom Spawn Points:")
            .color(NamedTextColor.GREEN));
        player.sendMessage(Component.text("─────────────────────")
            .color(NamedTextColor.GRAY));
        
        for (String worldName : spawnsSection.getKeys(false)) {
            double x = config.getDouble("custom-spawns." + worldName + ".x");
            double y = config.getDouble("custom-spawns." + worldName + ".y");
            double z = config.getDouble("custom-spawns." + worldName + ".z");
            
            player.sendMessage(Component.text("• " + worldName + ":")
                .color(NamedTextColor.YELLOW));
            player.sendMessage(Component.text("  X: " + (int)x + ", Y: " + (int)y + ", Z: " + (int)z)
                .color(NamedTextColor.GRAY));
        }
        
        return true;
    }
}
