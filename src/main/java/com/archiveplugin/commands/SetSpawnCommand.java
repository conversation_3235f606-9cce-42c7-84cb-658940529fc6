package com.archiveplugin.commands;

import com.archiveplugin.ArchivePlugin;
import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.format.NamedTextColor;
import org.bukkit.Location;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;

public class SetSpawnCommand implements CommandExecutor {
    
    private final ArchivePlugin plugin;

    public SetSpawnCommand(ArchivePlugin plugin) {
        this.plugin = plugin;
    }
    
    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!(sender instanceof Player player)) {
            sender.sendMessage(Component.text("This command can only be used by players!")
                .color(NamedTextColor.RED));
            return true;
        }
        
        if (!player.hasPermission("archiveplugin.admin")) {
            player.sendMessage(Component.text("You don't have permission to use this command!")
                .color(NamedTextColor.RED));
            return true;
        }
        
        if (args.length == 0) {
            // Set spawn for current world
            Location loc = player.getLocation();
            String worldName = loc.getWorld().getName();
            
            plugin.getSpawnManager().setCustomSpawn(worldName, loc);
            
            player.sendMessage(Component.text("Spawn point set for world '" + worldName + "' at:")
                .color(NamedTextColor.GREEN));
            player.sendMessage(Component.text("X: " + (int)loc.getX() + ", Y: " + (int)loc.getY() + ", Z: " + (int)loc.getZ())
                .color(NamedTextColor.YELLOW));
            
            return true;
        }
        
        if (args.length == 1) {
            String worldName = args[0];
            Location loc = player.getLocation();
            
            plugin.getSpawnManager().setCustomSpawn(worldName, loc);
            
            player.sendMessage(Component.text("Spawn point set for world '" + worldName + "' at:")
                .color(NamedTextColor.GREEN));
            player.sendMessage(Component.text("X: " + (int)loc.getX() + ", Y: " + (int)loc.getY() + ", Z: " + (int)loc.getZ())
                .color(NamedTextColor.YELLOW));
            
            return true;
        }
        
        // Show usage
        player.sendMessage(Component.text("Usage:")
            .color(NamedTextColor.YELLOW));
        player.sendMessage(Component.text("/setarchivespawn - Set spawn for current world")
            .color(NamedTextColor.GRAY));
        player.sendMessage(Component.text("/setarchivespawn <world> - Set spawn for specified world")
            .color(NamedTextColor.GRAY));
        
        return true;
    }
}
