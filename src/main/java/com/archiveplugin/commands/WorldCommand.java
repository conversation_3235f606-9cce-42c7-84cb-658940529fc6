package com.archiveplugin.commands;

import com.archiveplugin.ArchivePlugin;
import com.archiveplugin.gui.WorldSelectorGUI;
import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.format.NamedTextColor;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;

public class WorldCommand implements CommandExecutor {
    
    private final ArchivePlugin plugin;

    public WorldCommand(ArchivePlugin plugin) {
        this.plugin = plugin;
    }
    
    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!(sender instanceof Player player)) {
            sender.sendMessage(Component.text("This command can only be used by players!")
                .color(NamedTextColor.RED));
            return true;
        }
        
        if (!player.hasPermission("archiveplugin.use")) {
            player.sendMessage(Component.text("You don't have permission to use this command!")
                .color(NamedTextColor.RED));
            return true;
        }
        
        plugin.getWorldSelectorGUI().openWorldSelector(player);
        return true;
    }
}
