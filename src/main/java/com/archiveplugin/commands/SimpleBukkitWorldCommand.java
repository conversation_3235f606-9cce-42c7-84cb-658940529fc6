package com.archiveplugin.commands;

import com.archiveplugin.ArchivePlugin;
import com.archiveplugin.gui.WorldSelectorGUI;
import org.bukkit.ChatColor;
import org.bukkit.Location;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.entity.Player;

public class SimpleBukkitWorldCommand implements CommandExecutor {
    
    private final ArchivePlugin plugin;

    public SimpleBukkitWorldCommand(ArchivePlugin plugin) {
        this.plugin = plugin;
    }
    
    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage(ChatColor.RED + "This command can only be used by players!");
            return true;
        }
        
        Player player = (Player) sender;
        
        // Check for spawn setting commands
        if (args.length > 0) {
            if (args[0].equalsIgnoreCase("setspawn")) {
                return handleSetSpawn(player, args);
            } else if (args[0].equalsIgnoreCase("listspawns")) {
                return handleListSpawns(player);
            }
        }
        
        if (!player.hasPermission("archiveplugin.use")) {
            player.sendMessage(ChatColor.RED + "You don't have permission to use this command!");
            return true;
        }
        
        plugin.getWorldSelectorGUI().openWorldSelector(player);
        return true;
    }
    
    private boolean handleSetSpawn(Player player, String[] args) {
        if (!player.hasPermission("archiveplugin.admin") && !player.isOp()) {
            player.sendMessage(ChatColor.RED + "You don't have permission to set spawn points!");
            return true;
        }
        
        Location loc = player.getLocation();
        String worldName = loc.getWorld().getName();
        
        // Save to config
        FileConfiguration config = plugin.getConfig();
        String path = "custom-spawns." + worldName;
        config.set(path + ".x", loc.getX());
        config.set(path + ".y", loc.getY());
        config.set(path + ".z", loc.getZ());
        config.set(path + ".yaw", loc.getYaw());
        config.set(path + ".pitch", loc.getPitch());
        
        plugin.saveConfig();
        
        player.sendMessage(ChatColor.GREEN + "✅ Spawn point set for world '" + worldName + "' at:");
        player.sendMessage(ChatColor.YELLOW + "📍 X: " + (int)loc.getX() + ", Y: " + (int)loc.getY() + ", Z: " + (int)loc.getZ());
        
        plugin.getLogger().info("Player " + player.getName() + " set spawn for world " + worldName + 
                               " at " + (int)loc.getX() + ", " + (int)loc.getY() + ", " + (int)loc.getZ());
        
        return true;
    }
    
    private boolean handleListSpawns(Player player) {
        if (!player.hasPermission("archiveplugin.admin") && !player.isOp()) {
            player.sendMessage(ChatColor.RED + "You don't have permission to list spawn points!");
            return true;
        }
        
        FileConfiguration config = plugin.getConfig();
        ConfigurationSection spawnsSection = config.getConfigurationSection("custom-spawns");
        
        if (spawnsSection == null || spawnsSection.getKeys(false).isEmpty()) {
            player.sendMessage(ChatColor.YELLOW + "❌ No custom spawn points set.");
            player.sendMessage(ChatColor.GRAY + "💡 Use /archiveworlds setspawn to set spawn points.");
            return true;
        }
        
        player.sendMessage(ChatColor.GREEN + "📋 Custom Spawn Points:");
        player.sendMessage(ChatColor.GRAY + "─────────────────────");
        
        for (String worldName : spawnsSection.getKeys(false)) {
            double x = config.getDouble("custom-spawns." + worldName + ".x");
            double y = config.getDouble("custom-spawns." + worldName + ".y");
            double z = config.getDouble("custom-spawns." + worldName + ".z");
            
            player.sendMessage(ChatColor.YELLOW + "🌍 " + worldName + ":");
            player.sendMessage(ChatColor.GRAY + "   📍 X: " + (int)x + ", Y: " + (int)y + ", Z: " + (int)z);
        }
        
        return true;
    }
}
