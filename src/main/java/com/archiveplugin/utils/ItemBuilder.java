package com.archiveplugin.utils;

import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.format.TextColor;
import net.kyori.adventure.text.format.TextDecoration;
import org.bukkit.Material;
import org.bukkit.enchantments.Enchantment;
import org.bukkit.inventory.ItemFlag;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.ArrayList;
import java.util.List;

public class ItemBuilder {
    
    private final ItemStack itemStack;
    private final ItemMeta itemMeta;
    
    public ItemBuilder(Material material) {
        this.itemStack = new ItemStack(material);
        this.itemMeta = itemStack.getItemMeta();
    }
    
    public ItemBuilder(Material material, int amount) {
        this.itemStack = new ItemStack(material, amount);
        this.itemMeta = itemStack.getItemMeta();
    }
    
    public ItemBuilder setDisplayName(String name) {
        itemMeta.displayName(Component.text(name)
                .color(TextColor.color(255, 255, 255))
                .decoration(TextDecoration.ITALIC, false));
        return this;
    }
    
    public ItemBuilder setDisplayName(Component name) {
        itemMeta.displayName(name);
        return this;
    }
    
    public ItemBuilder setLore(List<String> lore) {
        List<Component> componentLore = new ArrayList<>();
        for (String line : lore) {
            componentLore.add(Component.text(line)
                    .color(TextColor.color(170, 170, 170))
                    .decoration(TextDecoration.ITALIC, false));
        }
        itemMeta.lore(componentLore);
        return this;
    }
    
    public ItemBuilder addLoreLine(String line) {
        List<Component> lore = itemMeta.lore();
        if (lore == null) {
            lore = new ArrayList<>();
        }
        lore.add(Component.text(line)
                .color(TextColor.color(170, 170, 170))
                .decoration(TextDecoration.ITALIC, false));
        itemMeta.lore(lore);
        return this;
    }
    
    public ItemBuilder addEnchantment(Enchantment enchantment, int level) {
        itemMeta.addEnchant(enchantment, level, true);
        return this;
    }
    
    public ItemBuilder addItemFlags(ItemFlag... flags) {
        itemMeta.addItemFlags(flags);
        return this;
    }
    
    public ItemBuilder setGlowing() {
        itemMeta.addEnchant(Enchantment.UNBREAKING, 1, true);
        itemMeta.addItemFlags(ItemFlag.HIDE_ENCHANTS);
        return this;
    }
    
    public ItemStack build() {
        itemStack.setItemMeta(itemMeta);
        return itemStack;
    }
}
