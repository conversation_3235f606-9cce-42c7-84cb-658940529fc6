package com.archiveplugin;

import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.World;
import org.bukkit.WorldCreator;
import org.bukkit.command.Command;
import org.bukkit.command.CommandSender;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.plugin.java.JavaPlugin;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ArchivePluginSimple extends JavaPlugin implements Listener {
    
    private static final int GUI_SIZE = 54;
    private static final int WORLDS_PER_PAGE = 28;
    
    private File worldsDirectory;
    private Map<Player, Integer> playerPages = new HashMap<>();
    
    @Override
    public void onEnable() {
        saveDefaultConfig();
        
        // Create worlds directory
        worldsDirectory = new File(getDataFolder().getParentFile(), "archive-worlds");
        if (!worldsDirectory.exists()) {
            worldsDirectory.mkdirs();
        }
        
        // Register events
        getServer().getPluginManager().registerEvents(this, this);
        
        // Load worlds
        loadAvailableWorlds();
        
        getLogger().info("ArchivePlugin Simple has been enabled!");
    }
    
    @Override
    public void onDisable() {
        getLogger().info("ArchivePlugin Simple has been disabled!");
    }
    
    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage(ChatColor.RED + "This command can only be used by players!");
            return true;
        }
        
        Player player = (Player) sender;
        String cmd = command.getName().toLowerCase();
        
        if (cmd.equals("archiveworlds") || cmd.equals("aw")) {
            if (args.length > 0) {
                String arg = args[0].toLowerCase();
                if (arg.equals("setspawn")) {
                    return handleSetSpawn(player);
                } else if (arg.equals("listspawns")) {
                    return handleListSpawns(player);
                }
            }
            
            if (!player.hasPermission("archiveplugin.use")) {
                player.sendMessage(ChatColor.RED + "You don't have permission to use this command!");
                return true;
            }
            
            openWorldSelector(player, 0);
            return true;
        }
        
        if (cmd.equals("setarchivespawn") || cmd.equals("setaspawn") || cmd.equals("sas")) {
            return handleSetSpawn(player);
        }
        
        if (cmd.equals("listarchivespawns") || cmd.equals("listaspawns") || cmd.equals("las")) {
            return handleListSpawns(player);
        }
        
        return false;
    }
    
    private boolean handleSetSpawn(Player player) {
        if (!player.hasPermission("archiveplugin.admin") && !player.isOp()) {
            player.sendMessage(ChatColor.RED + "You don't have permission to set spawn points!");
            return true;
        }
        
        Location loc = player.getLocation();
        String worldName = loc.getWorld().getName();
        
        // Save to config
        FileConfiguration config = getConfig();
        String path = "custom-spawns." + worldName;
        config.set(path + ".x", loc.getX());
        config.set(path + ".y", loc.getY());
        config.set(path + ".z", loc.getZ());
        config.set(path + ".yaw", loc.getYaw());
        config.set(path + ".pitch", loc.getPitch());
        
        saveConfig();
        
        player.sendMessage(ChatColor.GREEN + "✅ Spawn point set for world '" + worldName + "' at:");
        player.sendMessage(ChatColor.YELLOW + "📍 X: " + (int)loc.getX() + ", Y: " + (int)loc.getY() + ", Z: " + (int)loc.getZ());
        
        getLogger().info("Player " + player.getName() + " set spawn for world " + worldName + 
                        " at " + (int)loc.getX() + ", " + (int)loc.getY() + ", " + (int)loc.getZ());
        
        return true;
    }
    
    private boolean handleListSpawns(Player player) {
        if (!player.hasPermission("archiveplugin.admin") && !player.isOp()) {
            player.sendMessage(ChatColor.RED + "You don't have permission to list spawn points!");
            return true;
        }
        
        FileConfiguration config = getConfig();
        ConfigurationSection spawnsSection = config.getConfigurationSection("custom-spawns");
        
        if (spawnsSection == null || spawnsSection.getKeys(false).isEmpty()) {
            player.sendMessage(ChatColor.YELLOW + "❌ No custom spawn points set.");
            player.sendMessage(ChatColor.GRAY + "💡 Use /setarchivespawn to set spawn points.");
            return true;
        }
        
        player.sendMessage(ChatColor.GREEN + "📋 Custom Spawn Points:");
        player.sendMessage(ChatColor.GRAY + "─────────────────────");
        
        for (String worldName : spawnsSection.getKeys(false)) {
            double x = config.getDouble("custom-spawns." + worldName + ".x");
            double y = config.getDouble("custom-spawns." + worldName + ".y");
            double z = config.getDouble("custom-spawns." + worldName + ".z");
            
            player.sendMessage(ChatColor.YELLOW + "🌍 " + worldName + ":");
            player.sendMessage(ChatColor.GRAY + "   📍 X: " + (int)x + ", Y: " + (int)y + ", Z: " + (int)z);
        }
        
        return true;
    }
    
    private void openWorldSelector(Player player, int page) {
        List<String> worlds = getAvailableWorlds();
        
        Inventory gui = Bukkit.createInventory(null, GUI_SIZE, 
            ChatColor.DARK_PURPLE + "" + ChatColor.BOLD + "Archive Worlds - Page " + (page + 1));
        
        // Add world items
        int startIndex = page * WORLDS_PER_PAGE;
        int endIndex = Math.min(startIndex + WORLDS_PER_PAGE, worlds.size());
        
        for (int i = startIndex; i < endIndex; i++) {
            String worldName = worlds.get(i);
            ItemStack worldItem = createWorldItem(worldName);
            gui.setItem(i - startIndex, worldItem);
        }
        
        // Add navigation items
        addNavigationItems(gui, page, worlds.size(), player);
        
        playerPages.put(player, page);
        player.openInventory(gui);
    }
    
    private ItemStack createWorldItem(String worldName) {
        ItemStack item = new ItemStack(Material.GRASS_BLOCK);
        ItemMeta meta = item.getItemMeta();
        meta.setDisplayName(ChatColor.GREEN + "" + ChatColor.BOLD + worldName);
        meta.setLore(Arrays.asList(
            ChatColor.GRAY + "Click to teleport to this world",
            ChatColor.YELLOW + "World: " + worldName
        ));
        item.setItemMeta(meta);
        return item;
    }
    
    private void addNavigationItems(Inventory gui, int currentPage, int totalWorlds, Player player) {
        int totalPages = (int) Math.ceil((double) totalWorlds / WORLDS_PER_PAGE);
        
        // Previous page button
        if (currentPage > 0) {
            ItemStack prevButton = new ItemStack(Material.ARROW);
            ItemMeta meta = prevButton.getItemMeta();
            meta.setDisplayName(ChatColor.YELLOW + "" + ChatColor.BOLD + "Previous Page");
            meta.setLore(Arrays.asList(ChatColor.GRAY + "Go to page " + currentPage));
            prevButton.setItemMeta(meta);
            gui.setItem(45, prevButton);
        }
        
        // Set Spawn Point button
        ItemStack setSpawnButton = new ItemStack(Material.COMPASS);
        ItemMeta spawnMeta = setSpawnButton.getItemMeta();
        spawnMeta.setDisplayName(ChatColor.GOLD + "" + ChatColor.BOLD + "Set Spawn Point");
        spawnMeta.setLore(Arrays.asList(
            ChatColor.GRAY + "Click to set spawn point for current world",
            ChatColor.DARK_GRAY + "Requires admin permissions",
            ChatColor.YELLOW + "Current world: " + player.getWorld().getName()
        ));
        setSpawnButton.setItemMeta(spawnMeta);
        gui.setItem(46, setSpawnButton);
        
        // Close button
        ItemStack closeButton = new ItemStack(Material.BARRIER);
        ItemMeta closeMeta = closeButton.getItemMeta();
        closeMeta.setDisplayName(ChatColor.RED + "" + ChatColor.BOLD + "Close");
        closeMeta.setLore(Arrays.asList(ChatColor.GRAY + "Click to close this menu"));
        closeButton.setItemMeta(closeMeta);
        gui.setItem(49, closeButton);
        
        // Next page button
        if (currentPage < totalPages - 1) {
            ItemStack nextButton = new ItemStack(Material.ARROW);
            ItemMeta meta = nextButton.getItemMeta();
            meta.setDisplayName(ChatColor.YELLOW + "" + ChatColor.BOLD + "Next Page");
            meta.setLore(Arrays.asList(ChatColor.GRAY + "Go to page " + (currentPage + 2)));
            nextButton.setItemMeta(meta);
            gui.setItem(53, nextButton);
        }
        
        // Page info
        ItemStack pageInfo = new ItemStack(Material.BOOK);
        ItemMeta pageInfoMeta = pageInfo.getItemMeta();
        pageInfoMeta.setDisplayName(ChatColor.AQUA + "" + ChatColor.BOLD + "Page " + (currentPage + 1) + "/" + Math.max(1, totalPages));
        pageInfoMeta.setLore(Arrays.asList(
            ChatColor.GRAY + "Total worlds: " + totalWorlds,
            ChatColor.GRAY + "Worlds per page: " + WORLDS_PER_PAGE
        ));
        pageInfo.setItemMeta(pageInfoMeta);
        gui.setItem(50, pageInfo);
    }

    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) {
        if (!(event.getWhoClicked() instanceof Player)) return;

        Player player = (Player) event.getWhoClicked();
        String title = event.getView().getTitle();
        if (!title.contains("Archive Worlds")) return;

        event.setCancelled(true);

        ItemStack clickedItem = event.getCurrentItem();
        if (clickedItem == null || clickedItem.getType() == Material.AIR) return;

        int slot = event.getSlot();

        // Handle navigation buttons
        if (slot == 45 && clickedItem.getType() == Material.ARROW) {
            // Previous page
            Integer currentPage = playerPages.get(player);
            if (currentPage != null && currentPage > 0) {
                openWorldSelector(player, currentPage - 1);
            }
            return;
        }

        if (slot == 53 && clickedItem.getType() == Material.ARROW) {
            // Next page
            Integer currentPage = playerPages.get(player);
            if (currentPage != null) {
                openWorldSelector(player, currentPage + 1);
            }
            return;
        }

        if (slot == 46 && clickedItem.getType() == Material.COMPASS) {
            // Set Spawn Point button
            player.closeInventory();
            playerPages.remove(player);
            handleSetSpawn(player);
            return;
        }

        if (slot == 49 && clickedItem.getType() == Material.BARRIER) {
            // Close
            player.closeInventory();
            playerPages.remove(player);
            return;
        }

        // Handle world selection
        if (slot < WORLDS_PER_PAGE && clickedItem.getType() == Material.GRASS_BLOCK) {
            ItemMeta meta = clickedItem.getItemMeta();
            if (meta != null && meta.getDisplayName() != null) {
                String worldName = ChatColor.stripColor(meta.getDisplayName());

                if (teleportPlayerToWorld(player, worldName)) {
                    player.sendMessage(ChatColor.GREEN + "Teleported to world: " + worldName);
                } else {
                    player.sendMessage(ChatColor.RED + "Failed to teleport to world: " + worldName);
                }

                player.closeInventory();
                playerPages.remove(player);
            }
        }
    }

    private List<String> getAvailableWorlds() {
        List<String> worlds = new ArrayList<>();

        if (!worldsDirectory.exists()) {
            return worlds;
        }

        File[] worldFolders = worldsDirectory.listFiles(File::isDirectory);
        if (worldFolders != null) {
            for (File worldFolder : worldFolders) {
                File levelDat = new File(worldFolder, "level.dat");
                if (levelDat.exists()) {
                    worlds.add(worldFolder.getName());
                }
            }
        }

        return worlds;
    }

    private void loadAvailableWorlds() {
        List<String> worlds = getAvailableWorlds();

        for (String worldName : worlds) {
            try {
                if (Bukkit.getWorld(worldName) == null) {
                    File serverWorldDir = new File(Bukkit.getWorldContainer(), worldName);
                    File archiveWorldDir = new File(worldsDirectory, worldName);

                    if (!serverWorldDir.exists() && archiveWorldDir.exists()) {
                        copyWorld(archiveWorldDir, serverWorldDir);
                    }

                    WorldCreator creator = new WorldCreator(worldName);
                    World world = creator.createWorld();

                    if (world != null) {
                        getLogger().info("Loaded world: " + worldName);
                    }
                }
            } catch (Exception e) {
                getLogger().warning("Failed to load world: " + worldName + " - " + e.getMessage());
            }
        }
    }

    private boolean teleportPlayerToWorld(Player player, String worldName) {
        World world = Bukkit.getWorld(worldName);

        if (world == null) {
            try {
                File serverWorldDir = new File(Bukkit.getWorldContainer(), worldName);
                File archiveWorldDir = new File(worldsDirectory, worldName);

                if (!serverWorldDir.exists() && archiveWorldDir.exists()) {
                    copyWorld(archiveWorldDir, serverWorldDir);
                }

                WorldCreator creator = new WorldCreator(worldName);
                world = creator.createWorld();
            } catch (Exception e) {
                getLogger().warning("Failed to load world for teleportation: " + worldName + " - " + e.getMessage());
                return false;
            }
        }

        if (world != null) {
            Location teleportLocation = getCustomSpawnLocation(worldName, world);
            player.teleport(teleportLocation);

            if (hasCustomSpawn(worldName)) {
                getLogger().info("Teleported " + player.getName() + " to custom spawn in " + worldName);
            } else {
                getLogger().info("Teleported " + player.getName() + " to default spawn in " + worldName);
            }

            return true;
        }

        return false;
    }

    private Location getCustomSpawnLocation(String worldName, World world) {
        FileConfiguration config = getConfig();
        String path = "custom-spawns." + worldName;

        if (config.contains(path)) {
            try {
                double x = config.getDouble(path + ".x");
                double y = config.getDouble(path + ".y");
                double z = config.getDouble(path + ".z");
                float yaw = (float) config.getDouble(path + ".yaw", 0);
                float pitch = (float) config.getDouble(path + ".pitch", 0);

                return new Location(world, x, y, z, yaw, pitch);
            } catch (Exception e) {
                getLogger().warning("Failed to load custom spawn for " + worldName + ": " + e.getMessage());
            }
        }

        return world.getSpawnLocation();
    }

    private boolean hasCustomSpawn(String worldName) {
        return getConfig().contains("custom-spawns." + worldName);
    }

    private void copyWorld(File source, File target) {
        try {
            if (!target.exists()) {
                target.mkdirs();
            }

            copyDirectory(source.toPath(), target.toPath());
        } catch (Exception e) {
            getLogger().warning("Failed to copy world from " + source.getName() + " to " + target.getName() + ": " + e.getMessage());
        }
    }

    private void copyDirectory(Path source, Path target) throws IOException {
        Files.walk(source).forEach(sourcePath -> {
            try {
                Path targetPath = target.resolve(source.relativize(sourcePath));
                if (Files.isDirectory(sourcePath)) {
                    Files.createDirectories(targetPath);
                } else {
                    Files.copy(sourcePath, targetPath, StandardCopyOption.REPLACE_EXISTING);
                }
            } catch (IOException e) {
                getLogger().warning("Failed to copy file: " + sourcePath + " - " + e.getMessage());
            }
        });
    }
}
