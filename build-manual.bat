@echo off
echo Manual build script for ArchivePlugin (without <PERSON>ven)
echo.

REM Check if Java is installed
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Java is not installed or not in PATH
    echo Please install Java 21+ and try again
    pause
    exit /b 1
)

echo Creating build directories...
if not exist "build" mkdir build
if not exist "build\classes" mkdir build\classes
if not exist "build\resources" mkdir build\resources

echo.
echo Downloading Paper API...
if not exist "libs" mkdir libs

REM Note: This is a simplified version. For full functionality, you need Paper API JAR
echo WARNING: This manual build requires Paper API JAR file
echo Please download paper-api-1.21.1-R0.1-SNAPSHOT.jar and place it in libs/ folder
echo.

if not exist "libs\paper-api-1.21.1-R0.1-SNAPSHOT.jar" (
    echo ERROR: Paper API JAR not found in libs/ folder
    echo.
    echo Please:
    echo 1. Create 'libs' folder
    echo 2. Download Paper API from https://repo.papermc.io/repository/maven-public/io/papermc/paper/paper-api/1.21.1-R0.1-SNAPSHOT/
    echo 3. Place the JAR file in libs/ folder
    echo 4. Run this script again
    echo.
    pause
    exit /b 1
)

echo Copying resources...
xcopy /E /I "src\main\resources\*" "build\resources\" >nul

echo.
echo Compiling Java sources...
javac -cp "libs\paper-api-1.21.1-R0.1-SNAPSHOT.jar" -d "build\classes" -sourcepath "src\main\java" src\main\java\com\archiveplugin\*.java src\main\java\com\archiveplugin\commands\*.java src\main\java\com\archiveplugin\gui\*.java src\main\java\com\archiveplugin\listeners\*.java src\main\java\com\archiveplugin\managers\*.java src\main\java\com\archiveplugin\utils\*.java

if %errorlevel% neq 0 (
    echo.
    echo ========================================
    echo COMPILATION FAILED!
    echo ========================================
    echo.
    echo Check the error messages above
    echo.
    pause
    exit /b 1
)

echo.
echo Creating JAR file...
cd build\classes
jar cf ..\..\ArchivePlugin-1.0.0.jar -C . . -C ..\resources .
cd ..\..

if exist "ArchivePlugin-1.0.0.jar" (
    echo.
    echo ========================================
    echo BUILD SUCCESSFUL!
    echo ========================================
    echo.
    echo The plugin JAR file: ArchivePlugin-1.0.0.jar
    echo.
    echo Copy this file to your server's plugins folder
    echo.
) else (
    echo.
    echo ========================================
    echo JAR CREATION FAILED!
    echo ========================================
    echo.
)

pause
