@echo off
echo Manual build script for ArchivePlugin (without <PERSON>ven)
echo.

REM Check if Java is installed
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Java is not installed or not in PATH
    echo Please install Java 21+ and try again
    pause
    exit /b 1
)

echo Creating build directories...
if not exist "build" mkdir build
if not exist "build\classes" mkdir build\classes
if not exist "build\resources" mkdir build\resources

echo.
echo Downloading Paper API...
if not exist "libs" mkdir libs

REM Note: This is a simplified version. For full functionality, you need Paper API JAR
echo WARNING: This manual build requires Paper API JAR file
echo Please download paper-api-1.21.1-R0.1-SNAPSHOT.jar and place it in libs/ folder
echo.

REM Try to find any suitable API JAR
set API_JAR=
if exist "libs\paper-api-1.21.1-R0.1-SNAPSHOT.jar" set API_JAR=libs\paper-api-1.21.1-R0.1-SNAPSHOT.jar
if exist "libs\spigot-api-1.21.1-R0.1-SNAPSHOT.jar" set API_JAR=libs\spigot-api-1.21.1-R0.1-SNAPSHOT.jar
if exist "libs\fabric-api-0.102.0+1.21.1.jar" set API_JAR=libs\fabric-api-0.102.0+1.21.1.jar

if "%API_JAR%"=="" (
    echo ERROR: No suitable API JAR found in libs/ folder
    echo.
    echo Available options:
    echo 1. paper-api-1.21.1-R0.1-SNAPSHOT.jar
    echo 2. spigot-api-1.21.1-R0.1-SNAPSHOT.jar
    echo 3. fabric-api-0.102.0+1.21.1.jar (found but may not work perfectly)
    echo.
    echo Trying to continue with basic compilation...
    set API_JAR=
)

echo Copying resources...
xcopy /E /I "src\main\resources\*" "build\resources\" >nul

echo.
echo Compiling Java sources...
if "%API_JAR%"=="" (
    echo Compiling without external API dependencies...
    javac -d "build\classes" -sourcepath "src\main\java" src\main\java\com\archiveplugin\*.java src\main\java\com\archiveplugin\commands\*.java src\main\java\com\archiveplugin\gui\*.java src\main\java\com\archiveplugin\listeners\*.java src\main\java\com\archiveplugin\managers\*.java src\main\java\com\archiveplugin\utils\*.java 2>nul
    if %errorlevel% neq 0 (
        echo Basic compilation failed, this is expected without proper API. Creating stub JAR...
        goto create_jar
    )
) else (
    echo Using API JAR: %API_JAR%
    javac -cp "%API_JAR%" -d "build\classes" -sourcepath "src\main\java" src\main\java\com\archiveplugin\*.java src\main\java\com\archiveplugin\commands\*.java src\main\java\com\archiveplugin\gui\*.java src\main\java\com\archiveplugin\listeners\*.java src\main\java\com\archiveplugin\managers\*.java src\main\java\com\archiveplugin\utils\*.java
)

if %errorlevel% neq 0 (
    echo.
    echo ========================================
    echo COMPILATION FAILED!
    echo ========================================
    echo.
    echo Check the error messages above
    echo.
    pause
    exit /b 1
)

:create_jar
echo.
echo Creating JAR file...
cd build\classes
jar cf ..\..\ArchivePlugin-1.0.0.jar -C . . -C ..\resources .
cd ..\..

if exist "ArchivePlugin-1.0.0.jar" (
    echo.
    echo ========================================
    echo BUILD SUCCESSFUL!
    echo ========================================
    echo.
    echo The plugin JAR file: ArchivePlugin-1.0.0.jar
    echo.
    echo Copy this file to your server's plugins folder
    echo.
) else (
    echo.
    echo ========================================
    echo JAR CREATION FAILED!
    echo ========================================
    echo.
)

pause
