@echo off
echo Building ArchivePlugin...
echo.

REM Check if <PERSON><PERSON> is installed
mvn --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: <PERSON><PERSON> is not installed or not in PATH
    echo Please install <PERSON><PERSON> and try again
    pause
    exit /b 1
)

echo Cleaning previous builds...
mvn clean

echo.
echo Compiling and packaging...
mvn package

if %errorlevel% equ 0 (
    echo.
    echo ========================================
    echo BUILD SUCCESSFUL!
    echo ========================================
    echo.
    echo The plugin JAR file is located at:
    echo target\ArchivePlugin-1.0.0.jar
    echo.
    echo Copy this file to your server's plugins folder
    echo.
) else (
    echo.
    echo ========================================
    echo BUILD FAILED!
    echo ========================================
    echo.
    echo Check the error messages above
    echo.
)

pause
