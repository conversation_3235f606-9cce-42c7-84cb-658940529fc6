# Быстрый способ найти координаты построек

## Метод 1: В игре (самый простой)

1. Зайдите в мир в одиночной игре
2. Найдите ваши постройки
3. Встаньте в центре главной постройки
4. Нажмите **F3** (откроется отладочная информация)
5. Найдите строки:
   ```
   XYZ: 123.456 / 67.000 / -89.123
   ```
6. Запишите целые числа: X=123, Y=67, Z=-89

## Метод 2: Через NBT Explorer (для продвинутых)

1. С<PERSON><PERSON><PERSON><PERSON>йте NBT Explorer
2. Откройте файл `level.dat` из папки мира
3. Найдите `Data > Player > Pos`
4. Запишите координаты

## Метод 3: Поиск через файлы региона

Если у вас есть большие постройки, они будут в файлах региона:
1. Откройте папку `region` в мире
2. Файлы называются `r.X.Z.mca`
3. Координаты чанка = X*16, Z*16

## Быстрая настройка для тестирования

Если не хотите искать точные координаты, попробуйте эти настройки в config.yml:

```yaml
worlds:
  teleport-strategy: "search"
  search:
    max-radius: 20  # Увеличенный радиус поиска
```

Плагин сам найдет ваши постройки!

## Пример готовой конфигурации

```yaml
worlds:
  teleport-strategy: "custom"
  custom-coordinates:
    # Замените на имена ваших миров и координаты
    world1: {x: 0, y: 70, z: 0}
    world2: {x: 100, y: 64, z: -100}
    world3: {x: -50, y: 80, z: 200}
```

## Если ничего не работает

1. Используйте стратегию "highest" - найдет самую высокую безопасную точку
2. Или оставьте "spawn" и постройте что-то рядом со spawn точкой

```yaml
worlds:
  teleport-strategy: "highest"
```
